#!/usr/bin/env python3
"""Simple test to verify imports work."""

print("Starting simple test...")

try:
    from PIL import Image
    print("✅ PIL import successful")
except ImportError as e:
    print(f"❌ PIL import failed: {e}")

try:
    import cloudinary
    print("✅ Cloudinary import successful")
except ImportError as e:
    print(f"❌ Cloudinary import failed: {e}")

try:
    from utils.image_resize import resize_image_to_max_size
    print("✅ Image resize utility import successful")
except ImportError as e:
    print(f"❌ Image resize utility import failed: {e}")

try:
    from utils.cloudinary_upload import configure_cloudinary
    print("✅ Cloudinary upload utility import successful")
except ImportError as e:
    print(f"❌ Cloudinary upload utility import failed: {e}")

print("Simple test completed!")
