# Thumbnail Generation System

This document describes the thumbnail generation system that automatically creates and uploads thumbnails to Cloudinary for images in the similarity database.

## Overview

The thumbnail generation system:
1. Queries the database for images without `thumb_url`
2. Downloads original images from their URLs
3. Resizes images to maximum 300KB while maintaining quality (skips if already under 300KB)
4. Uploads resized images to Cloudinary in the `simfoni-thumbs` directory
5. Updates the database with the Cloudinary thumbnail URLs

## Prerequisites

### 1. Cloudinary Account Setup
You need a Cloudinary account and API credentials. Set these environment variables:

```bash
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

### 2. Dependencies
The system requires these Python packages (already added to Pipfile):
- `cloudinary` - For uploading to Cloudinary
- `PIL` (Pillow) - For image processing
- `requests` - For downloading images

## Files Added/Modified

### New Files
- `utils/image_resize.py` - Image resizing utilities
- `utils/cloudinary_upload.py` - Cloudinary upload utilities
- `app/services/thumbnail_generator.py` - Main thumbnail generation service
- `app/api/v1/endpoints/thumbnails.py` - API endpoints for thumbnail generation

### Modified Files
- `Pipfile` - Added cloudinary dependency
- `app/core/config.py` - Added Cloudinary configuration settings
- `.env.example` - Added Cloudinary environment variables
- `main.py` - Added thumbnails router

## API Endpoints

### 1. Generate Thumbnails (Synchronous)
```
POST /v1/thumbnails/generate
```

**Request Body:**
```json
{
  "limit": 100,        // Optional: max number of images to process
  "batch_size": 10     // Optional: batch size for processing
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Processed 50 images: 45 successful, 5 failed",
  "details": {
    "total_processed": 50,
    "successful": 45,
    "failed": 5,
    "failed_details": [...]
  }
}
```

### 2. Generate Thumbnails (Background)
```
POST /v1/thumbnails/generate-background
```

Starts thumbnail generation as a background task and returns immediately.

### 3. Check Status
```
GET /v1/thumbnails/status
```

Returns information about images that need thumbnail generation.

## Usage Examples

### Using curl

1. **Check status:**
```bash
curl -X GET "http://localhost:8000/v1/thumbnails/status" \
  -H "public-key: your-api-key"
```

2. **Generate thumbnails (sync):**
```bash
curl -X POST "http://localhost:8000/v1/thumbnails/generate" \
  -H "Content-Type: application/json" \
  -H "public-key: your-api-key" \
  -d '{"limit": 50, "batch_size": 10}'
```

3. **Generate thumbnails (background):**
```bash
curl -X POST "http://localhost:8000/v1/thumbnails/generate-background" \
  -H "Content-Type: application/json" \
  -H "public-key: your-api-key" \
  -d '{"limit": 100, "batch_size": 20}'
```

### Using Python

```python
import requests

# Check status
response = requests.get(
    "http://localhost:8000/v1/thumbnails/status",
    headers={"public-key": "your-api-key"}
)
print(response.json())

# Generate thumbnails
response = requests.post(
    "http://localhost:8000/v1/thumbnails/generate",
    headers={"public-key": "your-api-key"},
    json={"limit": 50, "batch_size": 10}
)
print(response.json())
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

### Image Processing Settings

The system uses these default settings:
- **Maximum file size:** 300KB
- **Quality range:** 95-20 (JPEG quality)
- **Minimum scale:** 30% of original size
- **Cloudinary folder:** `simfoni-thumbs`

## Database Schema

The system adds a `thumb_url` field to existing image documents:

```json
{
  "_id": "...",
  "url": "https://original-image-url.jpg",
  "title": "Image Title",
  "category": "Category",
  "thumb_url": "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/simfoni-thumbs/image_abc123.jpg"
}
```

## Error Handling

The system handles various error scenarios:
- **Network errors:** Retries and logs failed downloads
- **Image processing errors:** Skips corrupted images
- **Cloudinary upload errors:** Logs and continues with next image
- **Database errors:** Logs and continues processing

## Monitoring and Logging

All operations are logged with appropriate levels:
- **INFO:** Normal processing progress
- **WARNING:** Non-critical issues (e.g., images already under 300KB)
- **ERROR:** Failed operations that don't stop the process

## Performance Considerations

- **Batch processing:** Images are processed in configurable batches
- **Memory management:** Images are processed one at a time to avoid memory issues
- **Network timeouts:** 30-second timeout for image downloads
- **Background processing:** Use background endpoints for large batches

## Troubleshooting

### Common Issues

1. **"Cloudinary is not configured"**
   - Check that all CLOUDINARY_* environment variables are set
   - Verify credentials are correct

2. **"Failed to download image"**
   - Check that image URLs are accessible
   - Verify network connectivity

3. **"Failed to resize image"**
   - Check that the image file is not corrupted
   - Verify PIL can open the image format

4. **Database connection errors**
   - Check MongoDB connection string
   - Verify database and collection exist

### Testing

Run the test script to verify functionality:

```bash
python test_thumbnail_generation.py
```

This will test:
- Image resizing functionality
- Cloudinary configuration
- Image download capability
- Database connection

## Security Notes

- API endpoints require authentication via `public-key` header
- Cloudinary credentials should be kept secure
- Consider rate limiting for production use
- Validate image URLs to prevent SSRF attacks
