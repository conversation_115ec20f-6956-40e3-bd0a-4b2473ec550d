from models.SimilarityModel import SimilarityRequest, SimilarityResponse, SimilarityResponseBatch, SimilarityCompareRequest
from app.services.similarity import Similarity
from fastapi import APIRouter, BackgroundTasks, Header, Request, Depends
from typing import Annotated
from typing import Union
from typing import Any
from utils.extract import get_base_url
import json
from utils.progress import generate_id
from routers.test_dependency import MyService
from ..app.dependencies.dependencies import get_similarity_service

SimilarityRouter = APIRouter(prefix='/v1/similarity')
similarityService = get_similarity_service()

@SimilarityRouter.get('/', response_model=SimilarityResponse) #, response_model = SimilarityResponse    
def find_similarity(url: str,  public_key: str = Header(), min_score: Union[float, None]=0.7)  -> Any :
    """
    Find similar images from a given image url
    """  
    result = similarityService.get_similar_images(url, min_score)
    if 'error_message' in result:
        return SimilarityResponse(message= result['error_message'], result=[], error=True)

    return SimilarityResponse(message= 'success', result=result)

@SimilarityRouter.post('/', response_model = SimilarityResponseBatch)    
def find_similarity_batch(body: SimilarityRequest, req: Request, tasks: BackgroundTasks, public_key: str = Header()):
    
    
    # if body.webhook != None and body.webhook.url != '':
    #     print('try to run in background task...')
    #     task_id = generate_id()
    #     tasks.add_task(similarityService.get_similar_image_batch, body.urls, body.min_score, body.webhook, task_id, body.image_urls)
    #     return SimilarityResponseBatch(message= 'running in background', result=[], progress_url=f'{get_base_url(req.url._url)}/progress?id={task_id}')
    
    # print('--- body request: ', json.dumps(body))    
    result = similarityService.get_similar_image_batch(body)        
    return SimilarityResponseBatch(message='success', result=result)

@SimilarityRouter.post('/compare', response_model = SimilarityResponseBatch)
def find_similarity_compare(body: SimilarityCompareRequest, public_key: str = Header()):
    result = similarityService.get_similar_image_compare(body)
    return result