from fastapi import APIRouter, BackgroundTasks, Request
from models.FileDownloadModel import FileDownloadRequest
from services.file_downloader import begin_dowload
from utils.extract import get_base_url
from utils.progress import generate_id

FileDownloadRouter = APIRouter(prefix='/v1/download')

@FileDownloadRouter.post('/')
async def file_download(body: FileDownloadRequest, req: Request, tasks: BackgroundTasks):
    if body.webhook != None and body.webhook.url != "":
        task_id = generate_id()
        tasks.add_task(begin_dowload, body, task_id)
        return {'message': 'running in background', 'progress_url': f'{get_base_url(req.url._url)}/progress?id={task_id}'}
    return begin_dowload(body)