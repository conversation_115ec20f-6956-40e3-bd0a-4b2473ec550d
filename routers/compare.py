from models.CompareModel import CompareModel, CompareResponse
from fastapi import APIRouter, BackgroundTasks, Header, Request
from services import compare as compare_service
from services.compare import ImageCompare

CompareRouter = APIRouter(prefix='/v1/compare')
imageCompare = ImageCompare()

@CompareRouter.post('/', response_model=CompareResponse)
def compare(body: CompareModel, public_key: str = Header()):    
    print('compare.....')
    return imageCompare.compare(body)
