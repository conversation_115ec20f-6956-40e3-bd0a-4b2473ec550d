from domain.repositories.vector_cache_repository import VectorCache
from typing import Optional
import pymongo
from os import environ
from datetime import datetime, timedelta
import logging
import time


class VectorCacheMongo(VectorCache):
    def __init__(self, mongo_client):
        logging.info('init VectorCacheMongo...')
        self.db = mongo_client['similarity']
        self.cache_collection = self.db['vector_cache']
        # Set timeout to 3 seconds (3000ms)
        self.timeout_ms = 3000
        
        try:
            # Create indexes with timeout
            # Index for key lookups
            self.cache_collection.create_index('key', unique=True)
            
            # TTL index that automatically deletes documents after expiration
            self.cache_collection.create_index(
                'expireAt',
                expireAfterSeconds=0  # Delete when current time >= expireAt
            )

            # Test the connection by inserting and retrieving a test document
            test_key = 'test-mongo-api-similarity'
            self.save(test_key, 'working')
            test_result = self.get(test_key)
            logging.info('MongoDB vector cache connection test: Success')
        except pymongo.errors.ConnectionFailure as e:
            logging.info('MongoDB vector cache connection test: FAILED -', str(e))
        except pymongo.errors.ExecutionTimeout as e:
            logging.info(f'MongoDB operation timed out during initialization: {e}')
        except Exception as e:
            logging.info(f'error initializing MongoDB vector cache... {e}')

    def get(self, key: str) -> Optional[str]:
        start = time.time() 
        # time.sleep(10)
        # Add maxTimeMS option to limit query execution time to 3 seconds
        result = self.cache_collection.find_one({'key': key}, max_time_ms = self.timeout_ms)
        if time.time() - start > 5.0:
            logging.info(f'[performance][cache-mongo] took { time.time() - start}')

        if result is None:
            return None  # Document not found
        
        # if result and result.get('expireAt') > datetime.utcnow():
        #     return result['value'].encode('utf-8')
        # return None
        return result['value'].encode('utf-8') if 'value' in result else None       

    def save(self, key: str, value) -> None:
        try:
            # Calculate expiration time (2 months from now, matching Redis implementation)
            expire_at = datetime.utcnow() + timedelta(days=60)  # 30 days * 2 months
            
            # Using upsert=True to update if exists, insert if doesn't
            # Add maxTimeMS option to limit execution time to 3 seconds
            self.cache_collection.update_one(
                {'key': key},
                {
                    '$set': {
                        'value': value,
                        'expireAt': expire_at,
                        'updatedAt': datetime.utcnow()
                    }
                },
                upsert=True
            )
            # logging.info('cache saved.....')
        except pymongo.errors.ExecutionTimeout as e:
            logging.info(f'MongoDB operation timed out while saving key {key}')
        except Exception as e:
            logging.info(f'error saving to MongoDB cache... key: {key}, type: {type(value)}, err: {e}') 
