
from annoy import AnnoyIndex
from infrastructure.persistence.similarity_repository_local_impl import SimilarityRepoLocal
from domain.repositories.similarity_vector import SimilarityVector


class AnnoyVector(SimilarityVector):
    def __init__(self, repo: SimilarityRepoLocal):
        self.annoy_index = AnnoyIndex(1280, 'angular')
        self.annoy_index.load('model/sfi_tree.ann')
        self.repo = repo
        print('init annoy.....')

    def save(self, vector_data):
        print('not supported..')

    def find_nearest_image(self, img_feature, top_n=15):
        nn = self.annoy_index.get_nns_by_vector(img_feature, top_n, include_distances=True)
        n = nn[0]
        d = nn[1]
        print(f'nearest -- {n}')
        result = []        
        for i, index in enumerate(n):  
            img_url =  self.repo.fetch_image_by_index(index)
            result.append({'url': img_url, 'feature': self.annoy_index.get_item_vector(index)})
        return result

    def find_by_url(self, url: str):
        """Find vector data by URL in <PERSON>oy implementation."""
        # For <PERSON><PERSON>, we need to search through the repository to find the index
        # This is not very efficient but works for the local implementation
        try:
            # Find the index for this URL
            for i in range(self.annoy_index.get_n_items()):
                img_url = self.repo.fetch_image_by_index(i)
                if img_url == url:
                    return {
                        'url': url,
                        'feature': self.annoy_index.get_item_vector(i)
                    }
            return None
        except Exception as e:
            print(f"Error finding vector by URL {url}: {e}")
            return None

    def fetch_all(self):
        return []