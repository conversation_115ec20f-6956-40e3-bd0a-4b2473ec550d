from domain.repositories.vector_cache_repository import Vector<PERSON>ache
from typing import Optional
import redis
from os import environ
import time
import logging

class VectorCacheRedis(VectorCache):
    def __init__(self):
        print('init VectorCacheRedis...', environ.get('REDIS_DB') or 1)
        self.pool = redis.ConnectionPool(
            host=environ.get('REDIS_HOST'), port=environ.get('REDIS_PORT'),
            # username=environ.get('REDIS_USERNAME') or '', # use your Redis user. More info https://redis.io/docs/latest/operate/oss_and_stack/management/security/acl/
            # password=environ.get('REDIS_PASSWORD'), # use your Redis password
            # db = environ.get('REDIS_DB') or 1
        )
        self.client = redis.StrictRedis(connection_pool=self.pool)
        try:
            response = self.client.ping()
            print('Redis connection test:', 'SUCCESS' if response else 'FAILED')
            self.client.set('redis-api-similarity', 'working', ex=3600 * 24 * 30 * 2)
        except redis.ConnectionError as e:
            print('Redis connection test: FAILED -', str(e))
        except Exception as e:
            print(f'error can not save to redis... {e}')

    def get(self, key: str) -> Optional[str]:
        start = time.time() 
        result = self.client.get(key)
        logging.info(f'[cache-mongo] took { time.time() - start}')
        return result 

    def save(self, key: str, value) -> None:        
        try:
            self.client.set(key, value, ex=3600 * 24 * 30 * 2) #secs hours days months
            # print('save...', key)
        except Exception as e:
            print(f'error save... {key}, type {type(value)}, err {e}')            
