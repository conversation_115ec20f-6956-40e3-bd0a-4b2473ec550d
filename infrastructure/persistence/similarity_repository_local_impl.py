from typing import Optional, List, Dict, Any
from dataclasses import asdict
import numpy as np
import urllib.parse
from domain.repositories.similarity_repository import SimilarityRepository
from domain.entities.photo import Photo, Event, User
import json


class SimilarityRepoLocal(SimilarityRepository):
    def __init__(self):
        self.img_urls = np.load('model/sfi_url.npz')['urls']
        self.photos = self.load_photos()
        self.profiles = self.load_profiles()
        print('model and data loaded...')
        print(f'-- total imgage urls: {len(self.img_urls)}')
        print(f'-- total photos: {len(self.photos)}')


    def fetch_image_detail(self, url) -> Optional[Dict[str, Any]]:
        for photo in self.photos:
            if photo['url'] == url:
                user = self.find_user(photo['user_id'])
                if user is None:
                    print('can not find user with id: ', photo['user_id'])
                    return None

                event = Event(
                    year=photo['tahun'],
                    name=photo['event_name'],
                    short_name=photo['event_shortname']
                )

                user_data = User(
                    name=user['nama_lengkap'],
                    honors=user['gelar_fotografi'],
                    club=user['klub'],
                    fp_id=user['id'],
                    email=user['email']
                )

                photo_data = Photo(
                    title=photo['judul_foto'],
                    category=photo['kategori'],
                    event=event,
                    user=user_data,
                    meta_data=photo.get('meta_data')
                )
                return photo_data.to_dict()

        return None

    def fetch_image_by_index(self, index) -> Optional[str]:
        return self.img_urls[index]

    def find_user(self, id):
        for user in self.profiles:
            if user['id'] == id:
                return user
        return None

    def load_photos(self):
        with open('model/photos.json') as f:
            photos = json.load(f)

        result = []
        for photo in photos:
            if 'results' not in photo: continue
            for img in photo['results']:
                if 'url' not in img:
                    img['url'] = f'https://simfoni.fpsi.or.id/data/accepted_photo/{img["folder"]}/{urllib.parse.quote(img["filename"])}'
                img['user_id'] = photo['query']['id']
                result.append(img)
        return result


    def load_profiles(self):
        with open('model/profiles.json') as f:
            profiles = json.load(f)

        result = []
        for profile in profiles:
            if 'results' not in profile: continue
            result.append(profile['results'])
        return result

    def get_event(self, event_id) -> Optional[Event]:
        """Get event by event_id"""
        # For local implementation, return None or implement based on your data structure
        return None

    def get_user(self, user_id) -> Optional[User]:
        """Get user by user_id"""
        # For local implementation, you could search through profiles
        # or return None if user data is not available locally
        for profile in self.profiles:
            if profile.get('id') == user_id:
                return User(
                    name=profile.get('name', ''),
                    fp_id=profile.get('fp_id', ''),
                    email=profile.get('email', ''),
                    honors=profile.get('honors'),
                    club=profile.get('club')
                )
        return None

    def fetch_clusters(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                      sort_by: str = "total_photos", sort_order: str = "desc",
                      limit: Optional[int] = None, max_images_per_cluster: Optional[int] = 100) -> List[Dict[str, Any]]:
        """
        Fetch cluster data grouped by cluster_id for local implementation.
        Note: This is a basic implementation for local testing.
        """
        # Group photos by cluster_id
        clusters = {}
        for photo in self.photos:
            cluster_id = photo.get('cluster_id', 0)
            if cluster_id not in clusters:
                clusters[cluster_id] = []
            clusters[cluster_id].append(photo)

        # Convert to result format
        result = []
        for cluster_id, images in clusters.items():
            # Filter by min/max photos
            if min_photos is not None and len(images) < min_photos:
                continue
            if max_photos is not None and len(images) > max_photos:
                continue

            cluster_data = {
                "cluster_id": cluster_id,
                "total_images": len(images),
                "images": []
            }

            # Limit images per cluster if specified
            images_to_process = images[:max_images_per_cluster] if max_images_per_cluster else images

            for image in images_to_process:
                image_data = {
                    "url": image["url"],
                    "title": image.get("judul_foto"),
                    "category": image.get("kategori"),
                    "id": image.get("id"),
                    "meta_data": image.get("meta_data")
                }
                cluster_data["images"].append(image_data)

            result.append(cluster_data)

        # Sort results
        if sort_by == "total_photos":
            reverse = sort_order.lower() == "desc"
            result.sort(key=lambda x: x["total_images"], reverse=reverse)

        # Apply limit
        if limit is not None:
            result = result[:limit]

        return result

    def find_image_cluster(self, image_url: str) -> Optional[Dict[str, Any]]:
        """
        Find which cluster an image belongs to for local implementation.
        """
        # Find the image
        target_photo = None
        for photo in self.photos:
            if photo['url'] == image_url:
                target_photo = photo
                break

        if not target_photo or 'cluster_id' not in target_photo:
            return None

        cluster_id = target_photo['cluster_id']

        # Get all images in the same cluster
        cluster_images = [photo for photo in self.photos if photo.get('cluster_id') == cluster_id]

        cluster_data = {
            "cluster_id": cluster_id,
            "total_images": len(cluster_images),
            "images": []
        }

        for image in cluster_images:
            image_data = {
                "url": image["url"],
                "title": image.get("judul_foto"),
                "category": image.get("kategori"),
                "id": image.get("id"),
                "meta_data": image.get("meta_data")
            }
            cluster_data["images"].append(image_data)

        return cluster_data

    async def batch_insert_photos(self, photos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Batch insert photos for local implementation.
        Note: This is a basic implementation for local testing.
        """
        successful = 0
        failed = 0
        failed_details = []

        for photo in photos:
            try:
                # Basic validation
                if 'url' not in photo:
                    raise ValueError("Missing required field: url")

                # Add to local storage (in memory)
                self.photos.append(photo)
                successful += 1

            except Exception as e:
                failed += 1
                failed_details.append({
                    'photo_id': photo.get('id', 'unknown'),
                    'error': str(e)
                })

        return {
            'status': 'success' if failed == 0 else 'partial',
            'message': 'All photos inserted successfully' if failed == 0 else 'Some photos failed to insert',
            'details': {
                'total_processed': len(photos),
                'successful': successful,
                'failed': failed,
                'failed_details': failed_details
            }
        }

    def fetch_clusters_simple(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                             sort_by: str = "total_photos", sort_order: str = "desc",
                             limit: Optional[int] = None, max_images_per_cluster: Optional[int] = 100) -> List[Dict[str, Any]]:
        """
        Fetch cluster data using simple approach for local implementation.
        """
        # This is the same as the regular fetch_clusters for local implementation
        # since it's already doing the work in Python
        return self.fetch_clusters(min_photos, max_photos, sort_by, sort_order, limit, max_images_per_cluster)



    def find_one_without_cluster_id(self) -> Optional[Dict[str, Any]]:
        """
        Find one document that doesn't have a cluster_id assigned.
        """
        for photo in self.photos:
            if 'cluster_id' not in photo or photo['cluster_id'] is None:
                return photo
        return None

    def get_max_cluster_id(self) -> int:
        """
        Get the maximum cluster_id currently in the database.
        """
        max_cluster_id = 0
        for photo in self.photos:
            if 'cluster_id' in photo and photo['cluster_id'] is not None:
                cluster_id = int(photo['cluster_id'])
                if cluster_id > max_cluster_id:
                    max_cluster_id = cluster_id
        return max_cluster_id

    def update_cluster_id(self, url: str, cluster_id: int) -> bool:
        """
        Update the cluster_id for a specific image URL.
        """
        for photo in self.photos:
            if photo['url'] == url:
                photo['cluster_id'] = cluster_id
                return True
        return False

    def fetch_images_paginated(self, page: int = 1, page_size: int = 20,
                             category: Optional[str] = None,
                             event_id: Optional[str] = None,
                             user_id: Optional[str] = None,
                             cluster_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Fetch images with pagination and optional filters.
        """
        try:
            # Apply filters
            filtered_photos = []
            for photo in self.photos:
                if category and photo.get('category') != category:
                    continue
                if event_id and photo.get('event_id') != event_id:
                    continue
                if user_id and photo.get('user_id') != user_id:
                    continue
                if cluster_id is not None and photo.get('cluster_id') != cluster_id:
                    continue
                filtered_photos.append(photo)

            total_count = len(filtered_photos)

            # Calculate pagination
            skip = (page - 1) * page_size
            paginated_photos = filtered_photos[skip:skip + page_size]

            # Convert to Photo objects
            photo_list = []
            for photo_data in paginated_photos:
                try:
                    event = self.get_event(photo_data.get('event_id')) if photo_data.get('event_id') else None
                    user = self.get_user(photo_data.get('user_id')) if photo_data.get('user_id') else None

                    photo = Photo(
                        id=photo_data.get('id'),
                        title=photo_data.get('title', ''),
                        category=photo_data.get('category', ''),
                        cluster_id=photo_data.get('cluster_id', 0),
                        event=event,
                        user=user,
                        meta_data=photo_data.get('meta_data')
                    )

                    photo_dict = photo.to_dict()
                    photo_dict['url'] = photo_data.get('url', '')

                    # Remove None values
                    if event is None and 'event' in photo_dict:
                        del photo_dict['event']
                    if user is None and 'user' in photo_dict:
                        del photo_dict['user']

                    photo_list.append(photo_dict)

                except Exception as e:
                    print(f"Error processing photo {photo_data.get('url', 'unknown')}: {e}")
                    continue

            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size
            has_next = page < total_pages
            has_prev = page > 1

            return {
                'images': photo_list,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_next': has_next,
                    'has_prev': has_prev
                },
                'filters': {
                    'category': category,
                    'event_id': event_id,
                    'user_id': user_id,
                    'cluster_id': cluster_id
                }
            }

        except Exception as e:
            return {
                'images': [],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': 0,
                    'total_pages': 0,
                    'has_next': False,
                    'has_prev': False
                },
                'filters': {
                    'category': category,
                    'event_id': event_id,
                    'user_id': user_id,
                    'cluster_id': cluster_id
                },
                'error': str(e)
            }
