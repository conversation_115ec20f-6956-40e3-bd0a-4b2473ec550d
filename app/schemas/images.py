from pydantic import BaseModel
from typing import Optional, List, Dict, Any

class ImageFilter(BaseModel):
    category: Optional[str] = None
    event_id: Optional[str] = None
    user_id: Optional[str] = None
    cluster_id: Optional[int] = None

class PaginationMetadata(BaseModel):
    page: int
    page_size: int
    total_count: int
    total_pages: int
    has_next: bool
    has_prev: bool

class ImageItem(BaseModel):
    id: Optional[str] = None
    url: str
    title: str
    category: str
    cluster_id: Optional[Any] = None
    event: Optional[Dict[str, Any]] = None
    user: Optional[Dict[str, Any]] = None
    meta_data: Optional[Dict[str, Any]] = None

class ImagesResponse(BaseModel):
    message: str
    images: List[ImageItem]
    pagination: PaginationMetadata
    filters: ImageFilter
    error: bool = False

class ImagesRequest(BaseModel):
    page: Optional[int] = 1
    page_size: Optional[int] = 20
    category: Optional[str] = None
    event_id: Optional[str] = None
    user_id: Optional[str] = None
    cluster_id: Optional[int] = None

    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "page_size": 20,
                "category": "portrait",
                "event_id": "event123",
                "user_id": "user456",
                "cluster_id": 42
            }
        }
