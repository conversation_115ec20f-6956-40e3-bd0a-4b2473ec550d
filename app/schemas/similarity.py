from pydantic import BaseModel
from typing import Optional, Union, List
from domain.entities.photo import Photo
from app.schemas.similarity_examples import SimilarityRequestExamples

class Webhook(BaseModel):
    url: str   

class ImageUrlCompare(BaseModel):
    url: str
    meta_data: Union[dict, None] = None

class ImageUrl(BaseModel):
    url: str
    meta_data: Union[dict, None] = None
    compare: Union[List[ImageUrlCompare], None] = None        

class SimilarityRequest(BaseModel):
    min_score: Optional[float] = 0.7
    urls: Optional[List[str]] = None
    webhook: Optional[Webhook] = None
    image_urls: Union[List[ImageUrl], None] = None

    class Config:
        json_schema_extra = {
            "examples": SimilarityRequestExamples
        }

class SimilarityResult(BaseModel):
    url: str
    similarity: float
    detail: Optional[Photo] = None
    meta_data: Union[dict, None] = None

class SimilarityResponse(BaseModel):
    message: str
    result: List[SimilarityResult]
    error: bool = False
 
class SimilarityResultBatch(BaseModel):
    url_original: str
    similar_images: List[SimilarityResult]
    meta_data: Union[dict, None] = None
    message: str = None
    error: bool = False

class SimilarityResponseBatch(BaseModel):
    message: str
    result: List[SimilarityResultBatch] = [] 
    progress_url: Union[str, None] = None     
    error: bool = False     

class SimilarityCompareRequest(BaseModel):
    min_score: Optional[float] = 0.7
    urls: Optional[List[str]] = None
    webhook: Optional[Webhook] = None
    image_urls: Union[List[ImageUrlCompare], None] = None    
