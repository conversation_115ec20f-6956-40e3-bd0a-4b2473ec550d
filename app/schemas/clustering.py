from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from enum import Enum


class SortOrder(str, Enum):
    ASC = "asc"
    DESC = "desc"


class ClusterImage(BaseModel):
    """Individual image in a cluster"""
    url: str
    title: Optional[str] = None
    category: Optional[str] = None
    id: Optional[str] = None
    event: Optional[Dict[str, Any]] = None
    user: Optional[Dict[str, Any]] = None
    meta_data: Optional[Dict[str, Any]] = None


class ClusterData(BaseModel):
    """Cluster data with images"""
    cluster_id: int
    images: List[ClusterImage]
    total_images: int = Field(..., description="Total number of images in this cluster")


class ClusterResponse(BaseModel):
    """Response model for cluster data"""
    message: str = "success"
    result: List[ClusterData]
    total_clusters: int = Field(..., description="Total number of clusters returned")
    error: bool = False


class ImageClusterResponse(BaseModel):
    """Response model for finding which cluster an image belongs to"""
    message: str = "success"
    cluster_id: Optional[int] = None
    cluster_data: Optional[ClusterData] = None
    error: bool = False


class ClusterQueryParams(BaseModel):
    """Query parameters for cluster filtering"""
    min_photos: Optional[int] = Field(None, ge=1, description="Minimum number of photos per cluster")
    max_photos: Optional[int] = Field(None, ge=1, description="Maximum number of photos per cluster")
    sort_by: Optional[str] = Field("total_photos", description="Sort by field (total_photos)")
    sort_order: Optional[SortOrder] = Field(SortOrder.DESC, description="Sort order (asc/desc)")
    limit: Optional[int] = Field(None, ge=1, le=1000, description="Maximum number of clusters to return")
    image_url: Optional[str] = Field(None, description="Find cluster containing this image URL")
