from fastapi import Depends
from app.interfaces.similarity_interface import ISimilarityService
from app.interfaces.external.photo_service import IPhotoService
from app.interfaces.feature_extraction_interface import IFeatureExtractionService
from app.interfaces.images_interface import IImagesService
from domain.repositories.similarity_repository import SimilarityRepository
from domain.repositories.similarity_vector import SimilarityVector
from app.services.sync import Sync
from app.containers import Container
from dependency_injector.wiring import inject, Provide
from app.services.photo_upload import PhotoUploadService
from app.services.clustering import Clustering
from app.services.clustering_service import ClusteringService
from app.services.thumbnail_generator import ThumbnailGeneratorService

container = Container()
# container.config.use_remote.from_env("USE_REMOTE_REPO", as_=lambda x: x.lower() in ["true", "yes", "1"])
container.config.use_remote.from_env(
    "USE_REMOTE_REPO",
    default="true",                        # ← if not set, act as if it were “true”
    as_=lambda x: x.lower() in ["true", "yes", "1"]
)

print('set container config....')
container.config.feature_extraction_model.from_env("FEATURE_EXTRACTION_MODEL", default="mobilenet")

@inject
def get_photo_service(
    photo_service: IPhotoService = Depends(Provide[Container.photo_service])
) -> IPhotoService:
    return photo_service

@inject
def get_similarity_service(
    similarity_service: ISimilarityService = Depends(Provide[Container.similarity_service])
) -> ISimilarityService:
    return similarity_service

@inject
def get_similarity_repo(
    repo: SimilarityRepository = Depends(Provide[Container.similarity_repo])
) -> SimilarityRepository:
    return repo

@inject
def get_similarity_vector(
    vector_db: SimilarityVector = Depends(Provide[Container.vector_db])
) -> SimilarityVector:
    return vector_db

@inject
def get_sync_service(
    sync: Sync = Depends(Provide[Container.sync_service])
) -> Sync:
    return sync

@inject
def get_photo_upload_service(
    similarity_service: ISimilarityService = Depends(get_similarity_service),
    sync_service: Sync = Depends(Provide[Container.sync_service])
) -> PhotoUploadService:
    """Get an instance of PhotoUploadService."""
    return PhotoUploadService(similarity_service, sync_service)

@inject
def get_clustering_service(clustering: Clustering = Depends(Provide[Container.clustering_service])):
    return clustering

@inject
def get_feature_extraction_service(
    feature_service: IFeatureExtractionService = Depends(Provide[Container.feature_extraction_service])
) -> IFeatureExtractionService:
    return feature_service

@inject
def get_clustering_api_service(
    clustering_service: ClusteringService = Depends(Provide[Container.clustering_api_service])
) -> ClusteringService:
    return clustering_service

@inject
def get_images_service(
    images_service: IImagesService = Depends(Provide[Container.images_service])
) -> IImagesService:
    return images_service

@inject
def get_thumbnail_service(
    service: ThumbnailGeneratorService = Depends(Provide[Container.thumbnail_generator_service])
    ) -> ThumbnailGeneratorService:
    return service    
