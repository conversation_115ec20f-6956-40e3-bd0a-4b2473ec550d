from fastapi import APIRouter, Depends, HTTPException
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from app.dependencies.dependencies import get_photo_upload_service
from app.services.photo_upload import PhotoUploadService
import logging

router = APIRouter(prefix="/photos", tags=["photos"])

class PhotoUploadRequest(BaseModel):
    """
    Request model for photo upload endpoint.
    """
    data: List[Dict[str, Any]] = Field(
        ...,  # ... means required
        description="List of photo data objects to process",
        example=[{
            "url": "https://example.com/photo1.jpg",
            "title": "Beautiful Sunset",
            "category": "Nature",
            "participant_name": "<PERSON>",
            "email": "<EMAIL>",
            "fp_id": "fp001",
            "honors": "EFIAP",
            "club": "Photography Club",
            "year": "2024",
            "event_shortname": "IPC2024",
            "event_name": "International Photo Circuit 2024",
            "meta_data": {
                "participant_id": "10.22.1",
                "code": "C.10.1",
                "jid": "456"
            }
        }]
    )
    max: Optional[int] = Field(
        None,
        description="Optional limit for number of photos to process",
        example=10,
        ge=1  # greater than or equal to 1
    )
    expired_at: Optional[int] = Field(
        None,
        description="Optional expiration timestamp in milliseconds since epoch",
        example=1744619529990
    )

    class Config:
        json_schema_extra = {
            "example": {
                "data": [{
                    "url": "https://example.com/photo1.jpg",
                    "title": "Beautiful Sunset",
                    "category": "Nature",
                    "participant_name": "John Doe",
                    "email": "<EMAIL>",
                    "honors": "EFIAP",
                    "club": "Photography Club",
                    "year": "2024",
                    "event_shortname": "IPC2024",
                    "event_name": "International Photo Circuit 2024",
                    "meta_data": {
                        "participant_id": "10.22.1",
                        "code": "C.10.1",
                        "jid": "456"
                    }
                }],
                "max": 10,
                "expired_at": 1744619529990
            }
        }

@router.post("/upload", 
    response_model=Dict[str, Any],
    summary="Upload and process photos",
    description="""
    Upload and process new photo data. This endpoint:
    - Saves photographer profile information
    - Processes and saves photos with their metadata
    - Extracts and stores image features for similarity search
    
    Required fields in photo data:
    - url: URL of the photo
    
    Optional fields:
    - title: Photo title
    - category: Photo category
    - participant_name: Photographer's name
    - email: Photographer's email
    - honors: Photography honors/distinctions
    - club: Photography club affiliation
    - year: Year of photo (defaults to current year)
    - event_shortname: Short name of the event
    - event_name: Full name of the event
    - meta_data: Any additional metadata as key-value pairs
    - expired_at: Expiration timestamp in milliseconds since epoch
    
    Returns processing results including success/failure counts and details.
    """,
    responses={
        200: {
            "description": "Successfully processed photos",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "message": "Photos and profiles processed successfully",
                        "details": {
                            "total_processed": 1,
                            "successful": 1,
                            "failed": 0,
                            "failed_details": []
                        }
                    }
                }
            }
        },
        500: {
            "description": "Processing error",
            "content": {
                "application/json": {
                    "example": {
                        "status": "error",
                        "message": "Failed to process photos",
                        "details": {
                            "total_processed": 1,
                            "successful": 0,
                            "failed": 1,
                            "failed_details": [
                                {"error": "Error message details"}
                            ]
                        }
                    }
                }
            }
        }
    }
)
async def upload_photos(
    request: PhotoUploadRequest,
    service: PhotoUploadService = Depends(get_photo_upload_service)
):
    """
    Upload and process new photo data.
    
    Args:
        request: PhotoUploadRequest containing the data to process
        service: PhotoUploadService instance for processing the data
        
    Returns:
        Dict containing the processing results
    """
    try:
        result = await service.process_upload(request.data, request.max, request.expired_at)
        return result
    except Exception as e:
        logging.error(f"Error processing photo upload: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing photo upload: {str(e)}"
        ) 