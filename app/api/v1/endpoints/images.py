from fastapi import API<PERSON>out<PERSON>, Depends, Header, Query, HTTPException
from typing import Optional, Any
from app.schemas.images import ImagesResponse, ImagesRequest, ImageFilter, PaginationMetadata, ImageItem
from app.interfaces.images_interface import IImagesService
from app.dependencies.dependencies import get_images_service
import logging

router = APIRouter(prefix="/images", tags=["images"])

@router.get('/', response_model=ImagesResponse)
def get_images(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(20, ge=1, le=100, description="Number of items per page (max 100)"),
    category: Optional[str] = Query(None, description="Filter by category"),
    event_id: Optional[str] = Query(None, description="Filter by event ID"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    cluster_id: Optional[int] = Query(None, description="Filter by cluster ID"),
    public_key: str = Header(),
    images_service: IImagesService = Depends(get_images_service)
) -> Any:
    """
    Fetch images with pagination and optional filters.

    - **page**: Page number (starts from 1)
    - **page_size**: Number of items per page (maximum 100)
    - **category**: Filter images by category
    - **event_id**: Filter images by event ID
    - **user_id**: Filter images by user ID
    - **cluster_id**: Filter images by cluster ID
    """
    try:
        # Use service layer to fetch images with business logic
        result = images_service.get_images_paginated(
            page=page,
            page_size=page_size,
            category=category,
            event_id=event_id,
            user_id=user_id,
            cluster_id=cluster_id
        )

        # Check if there was an error in the service
        if 'error' in result:
            logging.error(f"Service error: {result['error']}")
            return ImagesResponse(
                message=result['error'],
                images=[],
                pagination=PaginationMetadata(**result['pagination']),
                filters=ImageFilter(**result['filters']),
                error=True
            )

        # Convert images to ImageItem objects
        image_items = []
        for image_data in result['images']:
            image_item = ImageItem(
                id=image_data.get('id'),
                url=image_data.get('url', ''),
                title=image_data.get('title', ''),
                category=image_data.get('category', ''),
                cluster_id=image_data.get('cluster_id'),
                event=image_data.get('event'),
                user=image_data.get('user'),
                meta_data=image_data.get('meta_data')
            )
            image_items.append(image_item)

        # Build response using service result
        return ImagesResponse(
            message="Images fetched successfully",
            images=image_items,
            pagination=PaginationMetadata(**result['pagination']),
            filters=ImageFilter(**result['filters']),
            error=False
        )

    except Exception as e:
        logging.error(f"Error in get_images endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while fetching images"
        )

@router.post('/', response_model=ImagesResponse)
def get_images_post(
    request: ImagesRequest,
    public_key: str = Header(),
    images_service: IImagesService = Depends(get_images_service)
) -> Any:
    """
    Fetch images with pagination and optional filters using POST method.

    This endpoint provides the same functionality as the GET endpoint but allows
    for more complex filter parameters to be passed in the request body.
    """
    try:
        # Extract parameters with defaults
        page = request.page or 1
        page_size = request.page_size or 20

        # Use service layer for validation and business logic
        result = images_service.get_images_paginated(
            page=page,
            page_size=page_size,
            category=request.category,
            event_id=request.event_id,
            user_id=request.user_id,
            cluster_id=request.cluster_id
        )

        # Handle service-level validation errors
        if 'error' in result:
            error_message = result['error']

            # Check if it's a validation error based on message content
            if any(word in error_message.lower() for word in ['page', 'size', 'must', 'cannot']):
                raise HTTPException(status_code=400, detail=error_message)

            # Otherwise it's a general service error
            return ImagesResponse(
                message=error_message,
                images=[],
                pagination=PaginationMetadata(**result['pagination']),
                filters=ImageFilter(**result['filters']),
                error=True
            )

        # Convert images to ImageItem objects
        image_items = []
        for image_data in result['images']:
            image_item = ImageItem(
                id=image_data.get('id'),
                url=image_data.get('url', ''),
                title=image_data.get('title', ''),
                category=image_data.get('category', ''),
                cluster_id=image_data.get('cluster_id'),
                event=image_data.get('event'),
                user=image_data.get('user'),
                meta_data=image_data.get('meta_data')
            )
            image_items.append(image_item)

        # Build response using service result
        return ImagesResponse(
            message="Images fetched successfully",
            images=image_items,
            pagination=PaginationMetadata(**result['pagination']),
            filters=ImageFilter(**result['filters']),
            error=False
        )

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in get_images_post endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while fetching images"
        )
