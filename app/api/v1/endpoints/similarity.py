from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depen<PERSON>, <PERSON><PERSON>, Request, BackgroundTasks, HTTPException
from typing import Union, Any
from app.interfaces.similarity_interface import ISimilarityService
from app.schemas.similarity import (
    SimilarityRequest, 
    SimilarityResponse, 
    SimilarityResponseBatch, 
    SimilarityCompareRequest
)
from app.dependencies.dependencies import get_similarity_service
from app.core.config import settings
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
import logging

router = APIRouter(prefix="/similarity", tags=["similarity"])

# @router.exception_handler(RequestValidationError)
# async def validation_exception_handler(request: Request, exc: RequestValidationError):
#     body = await request.body()
#     logging.error(
#         "Validation Error Details:\n"
#         f"URL: {request.url}\n"
#         f"Method: {request.method}\n"
#         f"Body: {body.decode()}\n"
#         f"Validation Error: {exc.errors()}"
#     )
#     return JSONResponse(
#         status_code=422,
#         content={"detail": exc.errors(), "body": body.decode()}
#     )

@router.get('/', response_model=SimilarityResponse)
def find_similarity(
    url: str,
    public_key: str = Header(),
    min_score: Union[float, None] = 0.7,
    similarity_service: ISimilarityService = Depends(get_similarity_service)
) -> Any:
    """Find similar images from a given image url"""
    result = similarity_service.get_similar_images(url, min_score)
    if 'error_message' in result:
        return SimilarityResponse(message=result['error_message'], result=[], error=True)
    return SimilarityResponse(message='success', result=result)

@router.post('/', response_model=SimilarityResponseBatch)
def find_similarity_batch(
    body: SimilarityRequest,
    req: Request,
    tasks: BackgroundTasks,
    public_key: str = Header(),
    similarity_service: ISimilarityService = Depends(get_similarity_service)
):
    if body.urls is not None and len(body.urls) > settings.MAX_BATCH_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"Batch size exceeds maximum of {settings.MAX_BATCH_SIZE}"
        )
    result = similarity_service.get_similar_image_batch(body)
    return SimilarityResponseBatch(message='success', result=result)

@router.post('/compare', response_model = SimilarityResponseBatch)
def find_similarity_compare(
    body: SimilarityCompareRequest, 
    public_key: str = Header(),
    similarity_service: ISimilarityService = Depends(get_similarity_service)):
    result = similarity_service.get_similar_image_compare(body)
    return result