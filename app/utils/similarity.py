import numpy as np

def calculate_similarity_matrix(vectors):
    """
    Calculate the cosine similarity matrix between all pairs of vectors.
    
    Args:
        vectors (numpy.ndarray): A 2D array where each row represents a vector.
        
    Returns:
        numpy.ndarray: A square matrix where element (i,j) represents the cosine similarity 
                      between vectors[i] and vectors[j]. Values range from -1 to 1, where:
                      1 indicates vectors point in same direction (most similar)
                      0 indicates vectors are orthogonal
                      -1 indicates vectors point in opposite directions (least similar)
    """
    # Calculate the dot product matrix
    dot_product_matrix = np.dot(vectors, vectors.T)
    # Calculate the magnitude of each vector
    magnitudes = np.linalg.norm(vectors, axis=1)
    # Calculate the outer product of the magnitudes
    outer_magnitudes = np.outer(magnitudes, magnitudes)
    # Calculate the cosine similarity matrix
    cosine_similarity_matrix = dot_product_matrix / outer_magnitudes
    print(cosine_similarity_matrix)
    return cosine_similarity_matrix