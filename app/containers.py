from dependency_injector import containers, providers
from infrastructure.persistence.similarity_repository_local_impl import SimilarityRepoLocal
from infrastructure.persistence.similarity_vector_annoy_impl import AnnoyVector
from infrastructure.persistence.similarity_vector_milvus_impl import MilvusVector
from infrastructure.persistence.similarity_repository_mongo_impl import SimilarityRepoMongo
from infrastructure.persistence.vector_cache_impl_mongo import VectorCacheMongo
from infrastructure.persistence.vector_cache_impl_redis import VectorCacheRedis
from app.services.similarity import SimilarityService
from app.services.images_service import ImagesService
from app.services.external.simfoni_service import SimfoniService
from app.services.external.external_photo_service import ExternalPhotoService
from app.services.sync import Sync
from app.services.clustering import Clustering
from app.services.clustering_service import ClusteringService
from configs import db
from app.core.config import settings
from app.services.feature_extraction.feature_extraction_mobilenet import FeatureExtractionService
from app.services.feature_extraction.feature_extraction_dino import DinoFeatureExtractionService
from app.services.thumbnail_generator import ThumbnailGeneratorService
import os


class Container(containers.DeclarativeContainer):
    # Configuration
    config = providers.Configuration()

    # Core dependencies
    db_mongo = providers.Singleton(db.get_db_mongo)
    db_milvus = providers.Singleton(MilvusVector)

    # vector_cache = providers.Singleton(VectorCacheRedis) #use redis as cache
    vector_cache = providers.Singleton(VectorCacheMongo, mongo_client=db_mongo) #use mongodb as cache

    # Repository layer
    similarity_repo = providers.Singleton(
        lambda use_remote, mongo_db:
            SimilarityRepoMongo(mongo_db) if use_remote
            else SimilarityRepoLocal(),
        use_remote=config.use_remote,
        mongo_db=db_mongo
    )

    # Vector database
    vector_db = providers.Singleton(
        lambda use_remote, repo, milvus:
            milvus if use_remote
            else AnnoyVector(repo),
        use_remote=config.use_remote,
        repo=similarity_repo,
        milvus=db_milvus
    )

    # Feature extraction service
    print('model -----> ', settings.FEATURE_EXTRACTION_MODEL)
    if settings.FEATURE_EXTRACTION_MODEL == "dino":
        feature_extraction_service = providers.Singleton(DinoFeatureExtractionService)
        print('use dino feature extraction')
    else:
        feature_extraction_service = providers.Singleton(FeatureExtractionService)
        print('use mobilenet....')

    # Services
    photo_service = providers.Singleton(
        SimfoniService
        # ExternalPhotoService
    )

    similarity_service = providers.Singleton(
        SimilarityService,
        repo=similarity_repo,
        repoVector=vector_db,
        repoCache=vector_cache,
        feature_service=feature_extraction_service
    )

    sync_service = providers.Singleton(
        Sync,
        photo_service=photo_service,
        dbClient=db_mongo,
        vectorRepo=db_milvus,
        feature_service=feature_extraction_service
    )

    clustering_service = providers.Singleton(
        Clustering,
        repo = similarity_repo,
        vectorRepo = db_milvus
    )

    clustering_api_service = providers.Singleton(
        ClusteringService,
        repo=similarity_repo
    )

    images_service = providers.Singleton(
        ImagesService,
        similarity_repo=similarity_repo
    )

    thumbnail_generator_service = providers.Singleton(ThumbnailGeneratorService, mongo_client=db_mongo)
