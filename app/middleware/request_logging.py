from fastapi import Request, status
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.exceptions import RequestValidationError
import logging
from typing import Callable
import json

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable):
        # Store request body for potential logging
        body = await self.get_request_body(request)
        
        try:
            response = await call_next(request)
            
            # If response status is 422, log the request details
            if response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY:
                await self.log_validation_error(request, body)
                
            return response
            
        except Exception as e:
            # Log other exceptions if needed
            if isinstance(e, RequestValidationError):
                await self.log_validation_error(request, body)
            raise e

    async def get_request_body(self, request: Request) -> str:
        try:
            body = await request.body()
            return body.decode()
        except Exception:
            return ""

    async def log_validation_error(self, request: Request, body: str):
        logging.error(
            "Validation Error (422) Details:\n"
            f"URL: {request.url}\n"
            f"Method: {request.method}\n"
            f"Headers: {dict(request.headers)}\n"
            f"Body: {body}\n"
        ) 