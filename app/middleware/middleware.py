from starlette.middleware.base import BaseHTTPMiddleware
import json 

private_routes = ['/v1/similarity']

class TokenValidationMiddleware(BaseHTTPMiddleware):
    
    async def dispatch(self, request, call_next):
        import time
        start_time = time.time()
        
        print(f'Request {request.method} -- {request.url}')
        if request.url.path in private_routes and 'Public-Key' not in request.headers:
            print('unauthorize request...')
            return Response('unauthorize',media_type='application/json', status_code=401)

        response = await call_next(request)
        
        process_time = time.time() - start_time
        print(f'Request to {request.url} completed in {process_time:.2f} seconds')
        
        return response