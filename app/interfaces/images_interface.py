from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class IImagesService(ABC):
    @abstractmethod
    def get_images_paginated(self, page: int = 1, page_size: int = 20,
                           category: Optional[str] = None,
                           event_id: Optional[str] = None,
                           user_id: Optional[str] = None,
                           cluster_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Fetch images with pagination and optional filters.

        Args:
            page: Page number (1-based)
            page_size: Number of items per page
            category: Filter by category
            event_id: Filter by event ID
            user_id: Filter by user ID
            cluster_id: Filter by cluster ID

        Returns:
            Dictionary containing paginated results with metadata
        """
        pass
