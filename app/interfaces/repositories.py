from abc import ABC, abstractmethod
from typing import Dict, List, Any

class ISimilarityRepository(ABC):
    @abstractmethod
    def fetch_image_detail(self, url: str) -> Dict[str, Any]:
        pass

class ISimilarityVector(ABC):
    @abstractmethod
    def find_nearest_image(self, img: Any) -> List[Dict[str, Any]]:
        pass

class IVectorCache(ABC):
    @abstractmethod
    def get(self, key: str) -> Any:
        pass

    @abstractmethod
    def save(self, key: str, value: str) -> None:
        pass 