
from abc import ABC, abstractmethod
from typing import Optional, List, Union
import numpy as np

class IFeatureExtractionService(ABC):
    @abstractmethod
    def extract_features(self, image: Optional[np.ndarray] = None, image_url: Optional[str] = None, image_urls: Optional[List[str]] = None) -> Union[np.ndarray, List[np.ndarray]]:
        """
        Extract features from an image, image URL, or multiple image URLs.

        Args:
            image: Optional numpy array representing the image
            image_url: Optional URL of the image
            image_urls: Optional list of URLs for batch processing

        Returns:
            A numpy array containing the extracted features for single image,
            or a list of numpy arrays for multiple images

        Raises:
            ValueError: If all parameters are None or if multiple parameters are provided
        """
        pass

