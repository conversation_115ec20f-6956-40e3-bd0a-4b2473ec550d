from abc import ABC, abstractmethod
from typing import List, Dict, Any, Union
from app.schemas.similarity import SimilarityRequest, SimilarityResponseBatch, SimilarityCompareRequest

class ISimilarityService(ABC):
    @abstractmethod
    def get_similar_images(self, url: str, min_score: float) -> Dict[str, Any]:
        """Find similar images from a given image url"""
        pass

    @abstractmethod
    def get_similar_image_batch(self, request: SimilarityRequest, task_id: str = None) -> List[Dict[str, Any]]:
        """Process batch of images to find similarities"""
        pass

    @abstractmethod
    def get_similar_image_compare(self, request: SimilarityCompareRequest) -> SimilarityResponseBatch:
        """Compare specific images for similarities"""
        pass 