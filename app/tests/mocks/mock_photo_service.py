from app.interfaces.external.photo_service import IPhotoService
from typing import List, Dict, Any

class MockPhotoService(IPhotoService):
    def fetch_profiles(self) -> List[Dict[str, Any]]:
        return [
            {
                "results": {
                    "id": "1",
                    "name": "Test User"
                }
            }
        ]

    def fetch_photos(self) -> List[Dict[str, Any]]:
        return [
            {
                "query": {"id": "1"},
                "results": [
                    {
                        "foto_id": "1",
                        "judul_foto": "Test Photo",
                        "kategori": "Test Category",
                        "event_shortname": "TEST2023",
                        "event_name": "Test Event 2023",
                        "tahun": "2023",
                        "folder": "test",
                        "filename": "test.jpg"
                    }
                ]
            }
        ]

    def fetch_photo_by_id(self, photo_id: str) -> Dict[str, Any]:
        return {
            "foto_id": photo_id,
            "judul_foto": f"Test Photo {photo_id}",
            "url": f"test_url_{photo_id}"
        } 