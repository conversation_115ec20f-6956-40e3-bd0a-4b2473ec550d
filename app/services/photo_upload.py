from typing import List, Dict, Any, Optional
from app.services.similarity import SimilarityService
from app.services.sync import Sync
import logging
from app.exceptions import PhotoUploadError
from datetime import datetime

class PhotoUploadService:
    def __init__(self, similarity_service: SimilarityService, sync_service: Sync):
        self.similarity_service = similarity_service
        self.sync_service = sync_service
        self.logger = logging.getLogger(__name__)

    async def process_upload(self, data: List[Dict[str, Any]], max_limit: Optional[int] = None, expired_at: Optional[int] = None) -> Dict[str, Any]:
        """
        Process a batch of photo uploads.
        
        Args:
            data: List of photo data objects to process
            max_limit: Optional maximum number of photos to process
            expired_at: Optional expiration timestamp in milliseconds
            
        Returns:
            Dict containing processing results and statistics
        """
        try:
            # Apply max limit if specified
            if max_limit is not None:
                data = data[:max_limit]
            
            total_photos = len(data)
            self.logger.info(f"Processing {total_photos} photos")
            
            try:
                # Transform and save profiles first
                profiles_data = self._transform_profiles_data(data)
                self.sync_service.save_profiles(profiles_data)
                self.logger.info("Profiles saved successfully")
                
                # Transform and save photos
                photos_data = self._transform_photos_data(data, expired_at)
                self.sync_service.save_photos(photos_data)
                self.logger.info("Photos saved successfully")
                
                return {
                    "status": "success",
                    "message": "Photos and profiles processed successfully",
                    "details": {
                        "total_processed": total_photos,
                        "successful": total_photos,
                        "failed": 0,
                        "failed_details": []
                    }
                }
                
            except Exception as e:
                self.logger.error(f"Failed to save data: {str(e)}")
                return {
                    "status": "error",
                    "message": f"Failed to save data: {str(e)}",
                    "details": {
                        "total_processed": total_photos,
                        "successful": 0,
                        "failed": total_photos,
                        "failed_details": [{"error": str(e)}]
                    }
                }
            
        except Exception as e:
            self.logger.error(f"Error processing upload batch: {str(e)}")
            raise PhotoUploadError(f"Failed to process upload: {str(e)}")

    def _transform_profiles_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Transform input data to match the expected profile format for sync service.
        
        Args:
            data: List of raw photo data objects
            
        Returns:
            List of transformed profile data matching sync service format
        """
        profiles = {}
        for photo in data:
            if not self._validate_photo_data(photo):
                continue
                
            user_id = photo.get('user_id') or photo.get('fp_id') or photo.get('email')
            if not user_id:
                continue
                
            # Only create profile once per user
            if user_id not in profiles:
                profiles[user_id] = {
                    'query': {'id': user_id},
                    'results': {
                        'id': user_id,
                        'nama_lengkap': photo.get('participant_name', ''),
                        'negara': photo.get('country'),
                        'email': photo.get('email', ''),
                        'gelar_fotografi': photo.get('honors', ''),
                        'klub': photo.get('club', '')
                    }
                }
        
        return list(profiles.values())

    def _transform_photos_data(self, data: List[Dict[str, Any]], expired_at: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Transform input data to match the expected photo format for sync service.
        
        Args:
            data: List of raw photo data objects
            expired_at: Optional expiration timestamp in milliseconds
            
        Returns:
            List of transformed photo data matching sync service format
        """
        current_year = str(datetime.now().year)
        
        # Group photos by user
        grouped_photos = {}
        for photo in data:
            if not self._validate_photo_data(photo):
                continue
                
            user_id = photo.get('user_id') or photo.get('fp_id') or photo.get('email')
            if not user_id:
                continue
                
            # Create photo data structure
            photo_data = {
                'query': {'id': user_id},
                'results': [{
                    'judul_foto': photo.get('title', ''),
                    'kategori': photo.get('category', ''),
                    'tahun': photo.get('year', current_year),
                    'event_shortname': photo.get('event_shortname', 'ONGOING'),
                    'event_name': photo.get('event_name', 'INTERNATIONAL PHOTO CIRCUIT'),
                    'url': photo['url'],
                    'meta_data': photo.get('meta_data', {}),
                    'expired_at': expired_at  # Add expiration timestamp if provided
                }]
            }
            
            if user_id in grouped_photos:
                grouped_photos[user_id]['results'].extend(photo_data['results'])
            else:
                grouped_photos[user_id] = photo_data
        
        return list(grouped_photos.values())

    def _validate_photo_data(self, photo_data: Dict[str, Any]) -> bool:
        """
        Validate the structure of a single photo data object.
        
        Args:
            photo_data: Photo data object to validate
            
        Returns:
            bool indicating if the data is valid
        """
        required_fields = ["url"]
        return all(field in photo_data for field in required_fields) 