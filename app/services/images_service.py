from typing import Dict, Any, Optional
import logging
from app.interfaces.images_interface import IImagesService
from domain.repositories.similarity_repository import SimilarityRepository

# Configure logging
logger = logging.getLogger(__name__)

class ImagesService(IImagesService):
    """
    Service for handling images-related business logic.
    This service implements the use case layer for image fetching operations.
    """

    def __init__(self, similarity_repo: SimilarityRepository):
        """
        Initialize the images service.

        Args:
            similarity_repo: Repository for accessing image data
        """
        self.similarity_repo = similarity_repo
        self.max_page_size = 100
        self.default_page_size = 20

    def get_images_paginated(self, page: int = 1, page_size: int = 20,
                           category: Optional[str] = None,
                           event_id: Optional[str] = None,
                           user_id: Optional[str] = None,
                           cluster_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Fetch images with pagination and optional filters.

        This method encapsulates the business logic for image retrieval including:
        - Parameter validation
        - Business rule enforcement
        - Error handling and logging
        - Data transformation

        Args:
            page: Page number (1-based)
            page_size: Number of items per page
            category: Filter by category
            event_id: Filter by event ID
            user_id: Filter by user ID
            cluster_id: Filter by cluster ID

        Returns:
            Dictionary containing:
            - images: List of image data
            - pagination: Pagination metadata
            - filters: Applied filters
            - error: Error message if any
        """
        try:
            # Validate business rules
            validation_result = self._validate_pagination_params(page, page_size)
            if not validation_result['valid']:
                return self._create_error_response(
                    validation_result['message'],
                    page,
                    page_size,
                    category,
                    event_id,
                    user_id,
                    cluster_id
                )

            # Log the request for monitoring
            logger.info(f"Fetching images: page={page}, page_size={page_size}, "
                       f"category={category}, event_id={event_id}, user_id={user_id}")

            # Apply business logic for filtering
            filters = self._prepare_filters(category, event_id, user_id, cluster_id)

            # Fetch data from repository
            repository_result = self.similarity_repo.fetch_images_paginated(
                page=page,
                page_size=page_size,
                category=filters['category'],
                event_id=filters['event_id'],
                user_id=filters['user_id'],
                cluster_id=filters['cluster_id']
            )

            # Handle repository errors
            if 'error' in repository_result:
                logger.error(f"Repository error: {repository_result['error']}")
                return self._create_error_response(
                    "Failed to fetch images from data source",
                    page,
                    page_size,
                    category,
                    event_id,
                    user_id,
                    cluster_id
                )

            # Transform and enrich the data
            processed_result = self._process_repository_result(
                repository_result,
                page,
                page_size,
                category,
                event_id,
                user_id,
                cluster_id
            )

            # Log success metrics
            total_count = processed_result['pagination']['total_count']
            images_returned = len(processed_result['images'])
            logger.info(f"Successfully fetched {images_returned} images "
                       f"(total: {total_count})")

            return processed_result

        except Exception as e:
            logger.error(f"Unexpected error in get_images_paginated: {str(e)}", exc_info=True)
            return self._create_error_response(
                "An unexpected error occurred while fetching images",
                page,
                page_size,
                category,
                event_id,
                user_id,
                cluster_id
            )

    def _validate_pagination_params(self, page: int, page_size: int) -> Dict[str, Any]:
        """
        Validate pagination parameters according to business rules.

        Args:
            page: Page number
            page_size: Page size

        Returns:
            Dictionary with validation result
        """
        if page < 1:
            return {
                'valid': False,
                'message': f"Page number must be greater than 0, got {page}"
            }

        if page_size < 1:
            return {
                'valid': False,
                'message': f"Page size must be greater than 0, got {page_size}"
            }

        if page_size > self.max_page_size:
            return {
                'valid': False,
                'message': f"Page size cannot exceed {self.max_page_size}, got {page_size}"
            }

        return {'valid': True, 'message': 'Valid parameters'}

    def _prepare_filters(self, category: Optional[str], event_id: Optional[str],
                        user_id: Optional[str], cluster_id: Optional[int]) -> Dict[str, Any]:
        """
        Prepare and validate filter parameters.

        Args:
            category: Category filter
            event_id: Event ID filter
            user_id: User ID filter
            cluster_id: Cluster ID filter

        Returns:
            Dictionary with prepared filters
        """
        filters = {
            'category': self._sanitize_filter_value(category),
            'event_id': self._sanitize_filter_value(event_id),
            'user_id': self._sanitize_filter_value(user_id),
            'cluster_id': self._validate_cluster_id(cluster_id)
        }

        # Log applied filters for monitoring
        active_filters = {k: v for k, v in filters.items() if v is not None}
        if active_filters:
            logger.info(f"Applying filters: {active_filters}")

        return filters

    def _validate_cluster_id(self, cluster_id: Optional[int]) -> Optional[int]:
        """
        Validate cluster ID filter value.

        Args:
            cluster_id: Cluster ID to validate

        Returns:
            Validated cluster ID or None
        """
        if cluster_id is None:
            return None

        # Ensure cluster_id is a valid integer
        if not isinstance(cluster_id, int):
            try:
                cluster_id = int(cluster_id)
            except (ValueError, TypeError):
                logger.warning(f"Invalid cluster_id value: {cluster_id}, ignoring filter")
                return None

        # Business rule: cluster_id should be non-negative
        if cluster_id < 0:
            logger.warning(f"Negative cluster_id not allowed: {cluster_id}, ignoring filter")
            return None

        return cluster_id

    def _sanitize_filter_value(self, value: Optional[str]) -> Optional[str]:
        """
        Sanitize filter values to prevent injection attacks.

        Args:
            value: Filter value to sanitize

        Returns:
            Sanitized value or None
        """
        if value is None:
            return None

        # Strip whitespace and return None for empty strings
        sanitized = value.strip()
        if not sanitized:
            return None

        # Additional sanitization can be added here if needed
        # For now, we just ensure it's a non-empty string
        return sanitized

    def _process_repository_result(self, repository_result: Dict[str, Any],
                                 page: int, page_size: int,
                                 category: Optional[str], event_id: Optional[str],
                                 user_id: Optional[str], cluster_id: Optional[int]) -> Dict[str, Any]:
        """
        Process and enrich repository result with business logic.

        Args:
            repository_result: Raw result from repository
            page: Requested page
            page_size: Requested page size
            category: Category filter
            event_id: Event ID filter
            user_id: User ID filter
            cluster_id: Cluster ID filter

        Returns:
            Processed result with business logic applied
        """
        # Extract data from repository result
        images = repository_result.get('images', [])
        pagination = repository_result.get('pagination', {})

        # Apply business logic transformations
        processed_images = self._enrich_image_data(images)

        # Ensure pagination metadata is complete
        processed_pagination = self._ensure_pagination_metadata(
            pagination, page, page_size
        )

        # Build the final result
        result = {
            'images': processed_images,
            'pagination': processed_pagination,
            'filters': {
                'category': category,
                'event_id': event_id,
                'user_id': user_id,
                'cluster_id': cluster_id
            },
            'metadata': {
                'request_timestamp': self._get_current_timestamp(),
                'total_filters_applied': len([f for f in [category, event_id, user_id, cluster_id] if f is not None])
            }
        }

        return result

    def _enrich_image_data(self, images: list) -> list:
        """
        Apply business logic to enrich image data.

        Args:
            images: List of raw image data

        Returns:
            List of enriched image data
        """
        enriched_images = []

        for image in images:
            # Create a copy to avoid modifying original data
            enriched_image = image.copy() if isinstance(image, dict) else image

            # Apply business logic enrichments
            if isinstance(enriched_image, dict):
                # Ensure required fields have default values
                enriched_image.setdefault('title', 'Untitled')
                enriched_image.setdefault('category', 'uncategorized')

                # Add computed fields if needed
                enriched_image['has_metadata'] = bool(enriched_image.get('meta_data'))
                enriched_image['has_user_info'] = bool(enriched_image.get('user'))
                enriched_image['has_event_info'] = bool(enriched_image.get('event'))

            enriched_images.append(enriched_image)

        return enriched_images

    def _ensure_pagination_metadata(self, pagination: Dict[str, Any],
                                  page: int, page_size: int) -> Dict[str, Any]:
        """
        Ensure pagination metadata is complete and valid.

        Args:
            pagination: Raw pagination data from repository
            page: Requested page
            page_size: Requested page size

        Returns:
            Complete pagination metadata
        """
        # Ensure all required fields are present with defaults
        complete_pagination = {
            'page': pagination.get('page', page),
            'page_size': pagination.get('page_size', page_size),
            'total_count': pagination.get('total_count', 0),
            'total_pages': pagination.get('total_pages', 0),
            'has_next': pagination.get('has_next', False),
            'has_prev': pagination.get('has_prev', False)
        }

        # Validate and fix inconsistencies
        total_count = complete_pagination['total_count']
        if total_count > 0:
            # Recalculate total_pages to ensure consistency
            complete_pagination['total_pages'] = (total_count + page_size - 1) // page_size

            # Recalculate has_next and has_prev
            complete_pagination['has_next'] = page < complete_pagination['total_pages']
            complete_pagination['has_prev'] = page > 1

        return complete_pagination

    def _create_error_response(self, message: str, page: int, page_size: int,
                             category: Optional[str], event_id: Optional[str],
                             user_id: Optional[str], cluster_id: Optional[int]) -> Dict[str, Any]:
        """
        Create a standardized error response.

        Args:
            message: Error message
            page: Requested page
            page_size: Requested page size
            category: Category filter
            event_id: Event ID filter
            user_id: User ID filter
            cluster_id: Cluster ID filter

        Returns:
            Standardized error response
        """
        return {
            'images': [],
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': 0,
                'total_pages': 0,
                'has_next': False,
                'has_prev': False
            },
            'filters': {
                'category': category,
                'event_id': event_id,
                'user_id': user_id,
                'cluster_id': cluster_id
            },
            'error': message,
            'metadata': {
                'request_timestamp': self._get_current_timestamp(),
                'total_filters_applied': len([f for f in [category, event_id, user_id, cluster_id] if f is not None])
            }
        }

    def _get_current_timestamp(self) -> str:
        """
        Get current timestamp in ISO format.

        Returns:
            Current timestamp as string
        """
        from datetime import datetime
        return datetime.utcnow().isoformat() + 'Z'

    def get_service_info(self) -> Dict[str, Any]:
        """
        Get service information and configuration.

        Returns:
            Service information
        """
        return {
            'service_name': 'ImagesService',
            'version': '1.0.0',
            'max_page_size': self.max_page_size,
            'default_page_size': self.default_page_size,
            'supported_filters': ['category', 'event_id', 'user_id', 'cluster_id']
        }
