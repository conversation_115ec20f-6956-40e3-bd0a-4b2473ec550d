from app.interfaces.external.photo_service import IPhotoService
from app.core.config import settings
import requests
from typing import List, Dict, Any
import urllib.parse
import logging

class SimfoniService(IPhotoService):
    def __init__(self):
        self.api_key = settings.SIMFONI_API_KEY
        self.base_url = settings.SIMFONI_BASE_URL
        logging.info(f'simfoni baseUrl {self.base_url}')

    def fetch_profiles(self, max = None) -> List[Dict[str, Any]]:
        # Implementation to fetch profiles from Simfoni service
        logging.info("Fetching profiles from Simfoni service")

        profiles = []
        index = 1
        failure = 0

        # Fetch last-id from API
        last_id = self.get_last_id()
        logging.info(f"Last ID from API: {last_id}")

        # Extract numeric part from last_id (e.g., "FP00564" -> 564)
        last_index = None
        if last_id and last_id.startswith("FP"):
            try:
                last_index = int(last_id[2:])  # Remove "FP" prefix and convert to int
                logging.info(f"Last index: {last_index}")
            except ValueError:
                logging.info(f"Could not parse last_id: {last_id}")

        while True:
            result = self.get_profile_by_id(f"FP{(index):05d}")
            if len(result) == 0:
                    logging.info(f'Failed Get profile: {index}')
                    failure += 1
                    if failure >= 5:
                        logging.info(f'got only profile: {len(profiles)}, last id: {index}, last id from API: {last_index}')
                        break
                    index += 1
                    continue

            profiles.append(result)
            index += 1
            failure = 0

            # Check if we've reached the last available ID
            if last_index and index > last_index:
                logging.info(f"Reached last available ID ({last_id}). Stopping.")
                break

            if len(profiles) % 100 == 0:
                    logging.info(f'>> Got {len(profiles)} profiles...')
            if max is not None and len(profiles) >= max:
                logging.info(f'-- return max profile: {len(profiles)}')
                return profiles

        return profiles

    def fetch_photos(self, max = None) -> List[Dict[str, Any]]:
        # Implementation to fetch photos from Simfoni service
        logging.info("Fetching photos from Simfoni service")
        photos = []
        index = 1
        failure = 0

        while True :   
            photo = self.get_photo_by_id(f"FP{(index):05d}")
            if len(photo) == 0:
                logging.info('Failed Get Photo: ', index) 
                failure += 1
                if failure >= 5:
                    logging.info('got only photo: ', len(photos)) 
                    break
                index += 1
                continue 
                
            photos.append(photo)
            index += 1
            failure = 0
            if len(photos) % 100 == 0:
                    logging.info(f'>> Got {len(photos)} photos...')
                    
            #return if max set
            if max is not None and len(photos) >= max:
                logging.info(f'-- return max photo: {len(photos)}')
                return photos

        return photos

    def get_photo_by_id(self, id):
        query = {'id': id}
        header = {'Key': self.api_key}
        resp = requests.get(f'{self.base_url}/accepted', params = query, headers = header)
        if resp.status_code == 200:
            return resp.json()['simfoni']
        else:
            logging.info(f'err... {resp.status_code}')
            query = {'id': id}
            return {}

    def get_profile_by_id(self, id):
        query = {'id': id}
        header = {'Key': self.api_key}
        resp = requests.get(f'{self.base_url}/member', params = query, headers = header)
        if resp.status_code == 200:
            return resp.json()['simfoni']
        else:
            logging.info(f'err... {resp.status_code}')
            query = {'id': id}
            return {}

    def get_last_id(self):
        """Fetch the last ID from the API"""
        try:
            resp = requests.get('https://api.simfoni-fpsi.web.id/get-last-id')
            if resp.status_code == 200:
                data = resp.json()
                return data.get('simfoni', {}).get('results', {}).get('last_id')
            else:
                logging.info(f'Error fetching last ID: {resp.status_code}')
                return None
        except Exception as e:
            logging.info(f'Exception while fetching last ID: {e}')
            return None

    def _get_headers(self) -> Dict[str, str]:
        return {"Authorization": f"Bearer {self.api_key}"}

    def construct_url(self, photo: Dict[str, Any]) -> str:
        """Helper method to construct photo URL"""
        if 'url' in photo and photo['url'].startswith('https://'):
            return photo['url']
        return f"https://simfoni.fpsi.or.id/data/accepted_photo/{photo['folder']}/{urllib.parse.quote(photo['filename'])}" 