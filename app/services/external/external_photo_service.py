from app.interfaces.external.photo_service import IPhotoService
from typing import List, Dict, Any
import json
from datetime import datetime

class ExternalPhotoService(IPhotoService):
    def __init__(self):
        print("init photo external")
        self.data = self.read_and_parse_json('Dataphoto.json')
        # print(f'---- data -----> {self.data}')

    def fetch_profiles(self, max = None) -> List[Dict[str, Any]]:
        profiles = []
        if 'data' in self.data and isinstance(self.data['data'], list):
            count = 0
            for item in self.data['data']:
                user_id = item.get('fp_id') or item.get('email')
                profile = {
                    'query': {'id': user_id},
                    'results': {
                        'id': user_id,
                        'nama_lengkap': item.get('participant_name'),
                        'negara': None,  # Assuming 'country' is not in your sample data
                        'email': item.get('email'),
                        'gelar_fotografi': item.get('honors'),
                        'klub': item.get('club')
                    }
                }
                profiles.append(profile)
                count += 1
                if max is not None and count >= max:
                    break
        return profiles

    def fetch_photos(self, max = None) -> List[Dict[str, Any]]:
        grouped_photos = {}
        current_year = datetime.now().year
        if 'data' in self.data and isinstance(self.data['data'], list):
            count = 0
            for item in self.data['data']:
                email = item.get('email')
                if email:
                    # Create meta_data from the specified fields
                    meta_data = {
                        'id': item.get('id'),
                        'code': item.get('code'),
                        'participant_no': item.get('participant_no'),
                        'event_code': item.get('event_code'),
                    }
                    
                    photo_data = {
                        'judul_foto': item.get('title'),
                        'kategori': item.get('category'),
                        'tahun': str(current_year),
                        "event_shortname": item.get('event_name', 'ONGOING'),
                        "event_name": item.get('event_name', 'ONGOING'),
                        "url": item.get('image_url'),
                        "meta_data": meta_data
                    }
                    
                    # Use fp_id if available, otherwise use email
                    profile_id = item.get('fp_id') or email
                    
                    if profile_id not in grouped_photos:
                        grouped_photos[profile_id] = {
                            'query': {'id': profile_id},
                            'results': [photo_data]
                        }
                    else:
                        grouped_photos[profile_id]['results'].append(photo_data)
                    count += 1
                    if max is not None and count >= max:
                        break
        result = []
        for profile_id, data in grouped_photos.items():
            result.append(data)
        return result

    def read_and_parse_json(self, file_path):
        """
        Reads a JSON file and parses its content into a Python dictionary or list.

        Args:
            file_path (str): The path to the JSON file.

        Returns:
            dict or list or None: The parsed JSON data as a Python dictionary or list,
                                    or None if an error occurred during reading or parsing.
        """
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                return data
        except FileNotFoundError:
            print(f"Error: File not found at '{file_path}'")
            return None
        except json.JSONDecodeError as e:
            print(f"Error: Could not decode JSON from '{file_path}'. Details: {e}")
            return None