from os import environ
import requests
from app.interfaces.external.photo_service import IPhotoService
from domain.repositories.similarity_vector import SimilarityVector
import pymongo
from bson.objectid import ObjectId
import urllib.parse
from utils.image import load_image_url
from app.interfaces.feature_extraction_interface import IFeatureExtractionService
import logging


#sync data from simfoni
class Sync:
    def __init__(self, photo_service: IPhotoService, dbClient: pymongo.MongoClient, vectorRepo: SimilarityVector, feature_service: IFeatureExtractionService):
        logging.info('Initializing sync service...')
        self.photo_service = photo_service
        self.db_name = 'similarity'
        self.db = dbClient[self.db_name]
        self.db_client = dbClient
        self.users_collection = self.db["users"]
        self.events_collection = self.db['events']
        self.images_collection = self.db['images']
        self.repo_vector = vectorRepo
        self.feature_service= feature_service

    # checking if sync should run based on certain condition
    def run_sync_job(self):
        # run sync (only if running from cloud run job)
        cloud_run_ex = environ.get('CLOUD_RUN_EXECUTION') 
        job_only = (environ.get('JOB_ONLY') or '').lower() in ["true", "yes", "1"]

        if environ.get('SYNC_VECTOR'):
            self.sync_image_vector()
            return True 
            
        if cloud_run_ex is not None and cloud_run_ex != '' or job_only:
            logging.info(f'Running cloud run job. Execution: {cloud_run_ex}, jobOnly: {job_only}')
            self.start_sync()
            return True

        if environ.get('FIX_DATA'):
            self.fix_data()
            return True

        return False

    def start_sync(self):
        logging.info('>> Starting sync process...')
        # self._delete_ongoing_images()  # Delete ONGOING images first
        self._sync_profile()
        self._sync_photo()
        logging.info(">> finish sync data...")

    def sync_image_vector(self):
        """
        Sync image vectors by inserting image features to vector db for images
        that exist in MongoDB but not in vector db.

        Process:
        1. Get images from MongoDB in batches of 50
        2. Check which images are not in vector db using fetch_by_url
        3. Extract features for missing images and insert to vector db
        """
        print('will only extract images not present in vector db...')
        logging.info('Starting sync_image_vector process...')

        batch_size = 10
        skip = 0
        total_processed = 0
        total_synced = 0

        try:
            while True:
                # Get batch of images from MongoDB
                images_batch = list(self.images_collection.find({}, {"url": 1}).skip(skip).limit(batch_size))

                if not images_batch:
                    logging.info(f"No more images to process. Total processed: {total_processed}, Total synced: {total_synced}")
                    break

                logging.info(f"Processing batch {skip//batch_size + 1}: {len(images_batch)} images")

                # Extract URLs from the batch
                urls = [img["url"] for img in images_batch]

                # Check which URLs are already in vector db
                existing_urls = self.repo_vector.fetch_by_url(urls)
                existing_urls_set = set(existing_urls)

                # Find URLs that are not in vector db
                missing_urls = [url for url in urls if url not in existing_urls_set]

                logging.info(f"Found {len(missing_urls)} images not in vector db out of {len(urls)} total")

                if missing_urls:
                    # Extract features for all missing images in batch
                    logging.info(f"Extracting features for {len(missing_urls)} missing images...")

                    try:
                        # Use batch feature extraction
                        features_batch = self.feature_service.extract_features(image_urls=missing_urls)

                        # Prepare vector data
                        vector_data = []
                        for i, (url, feature) in enumerate(zip(missing_urls, features_batch)):
                            if feature is not None and len(feature) > 0:
                                vector_data.append({'url': url, 'vector': feature})
                            else:
                                logging.warning(f"Failed to extract features for image: {url}")

                        logging.info(f"Successfully extracted features for {len(vector_data)} out of {len(missing_urls)} images")

                    except Exception as e:
                        logging.error(f"Error in batch feature extraction: {str(e)}")
                        # Fallback to individual processing
                        logging.info("Falling back to individual feature extraction...")
                        vector_data = []

                        for i, url in enumerate(missing_urls):
                            try:
                                logging.debug(f"Extracting features for image {i+1}/{len(missing_urls)}: {url}")

                                # Extract image feature using feature service
                                img_feature = self.feature_service.extract_features(image_url=url)

                                if img_feature is None or len(img_feature) == 0:
                                    logging.warning(f"Failed to extract features for image: {url}")
                                    continue

                                vector_data.append({'url': url, 'vector': img_feature})

                            except Exception as e:
                                logging.error(f"Error processing image {url}: {str(e)}")
                                continue

                    # Insert to vector db if we have valid features
                    if vector_data:
                        try:
                            self.repo_vector.save(vector_data)
                            total_synced += len(vector_data)
                            logging.info(f"Successfully synced {len(vector_data)} image vectors to vector db")
                        except Exception as e:
                            logging.error(f"Error saving vectors to db: {str(e)}")
                            raise

                total_processed += len(images_batch)
                skip += batch_size

                # Log progress
                logging.info(f"Progress: processed {total_processed} images, synced {total_synced} new vectors")

        except Exception as e:
            logging.error(f"Error in sync_image_vector: {str(e)}")
            raise

        logging.info(f"sync_image_vector completed. Total processed: {total_processed}, Total synced: {total_synced}")

    def _delete_ongoing_images(self):
        """
        Deletes all images from the database where the event_name is "ONGOING".
        This is done to ensure we don't have duplicate entries when syncing new data.
        """
        logging.info('Deleting ONGOING images...')
        try:
            # First, find the event_id for ONGOING events
            ongoing_events = self.events_collection.find({"event_name": "INTERNATIONAL PHOTO CIRCUIT"})
            deleted_count = 0
            
            for event in ongoing_events:
                # Delete images associated with this event
                result = self.images_collection.delete_many({"event_id": event["_id"]})
                deleted_count += result.deleted_count
                
                # Delete the event itself
                self.events_collection.delete_one({"_id": event["_id"]})
            
            logging.info(f"Deleted {deleted_count} images with ONGOING event")
            
        except Exception as e:
            logging.error(f"Error deleting ONGOING images: {str(e)}")
            raise

    def _sync_profile(self):
        logging.info('Syncing profiles...')
        profiles = self.photo_service.fetch_profiles()
        self.save_profiles(profiles)

    def save_profiles(self, profiles):
        # Build the requests  
        requests = []
        for user in profiles:
            requests.append(
                pymongo.ReplaceOne(
                    {"id": user["results"]["id"]}, 
                    user["results"], 
                    upsert=True
                )
            )
        
        response = self.users_collection.bulk_write(requests)
        logging.info(f"Inserted {response.upserted_count} new users")

    def _sync_photo(self):
        logging.info('Syncing photos...')
        photos = self.photo_service.fetch_photos()
        logging.info(f"# photo size {len(photos)}")

        # Step 1: Save all new photos to MongoDB
        self.save_photos(photos)

        # Step 2: Extract features and save to vector DB for photos not in vector DB
        logging.info('Starting feature extraction and vector DB sync...')
        self.sync_image_vector()

    def save_photos(self, photos):
        """
        Save photos to MongoDB only. Feature extraction and vector DB insertion
        will be handled separately by sync_image_vector().

        Process:
        1. Sync events first
        2. Find new photos not in MongoDB
        3. Insert ALL new photos to MongoDB in batches
        """
        events = self._get_unique_events(photos)
        self._sync_event(events)

        new_photos = self._get_new_photos(photos)
        if len(new_photos) == 0:
            logging.info("No new photos to sync")
            return

        logging.info(f"Found {len(new_photos)} new photos to insert to MongoDB")
        batch_size = 50  # adjust as needed

        with self.db_client.start_session() as session:
            with session.start_transaction():
                db = session.client[self.db_name]
                collection = db.images

                # Process photos in batches - insert to MongoDB only
                total_inserted = 0
                for batch_start in range(0, len(new_photos), batch_size):
                    batch_end = min(batch_start + batch_size, len(new_photos))
                    batch_photos_subset = new_photos[batch_start:batch_end]

                    logging.info(f"Inserting batch {batch_start//batch_size + 1}: {len(batch_photos_subset)} photos to MongoDB")

                    try:
                        # Insert all photos in this batch to MongoDB
                        result = collection.insert_many(batch_photos_subset)
                        total_inserted += len(result.inserted_ids)
                        logging.info(f"Successfully inserted batch of {len(result.inserted_ids)} images to MongoDB")

                        logging.info(f'Processed photos {batch_end} / {len(new_photos)}, progress: {(batch_end/len(new_photos))*100:.1f}%')

                    except Exception as e:
                        logging.error(f'Error inserting batch {batch_start//batch_size + 1} to MongoDB: {str(e)}')
                        raise  # Re-raise to trigger transaction rollback

                logging.info(f"Successfully inserted {total_inserted} new photos to MongoDB")
                logging.info("Feature extraction and vector DB insertion will be handled by sync_image_vector()")

    def _sync_photo_v1(self):        
        logging.info('Syncing photos...')
        photos = self.photo_service.fetch_photos()
        logging.info(f"# photo size {len(photos)}")
        events = self._get_unique_events(photos)
        self._sync_event(events)
        new_photos = self._get_new_photos(photos)
        if len(new_photos) == 0:
            logging.info("No new photos to sync")
            return  

        # Extract features for all images using batch processing
        logging.info(f"Extracting features for {len(new_photos)} new photos...")
        urls = [p['url'] for p in new_photos]

        try:
            features_batch = self.feature_service.extract_features(image_urls=urls)
            vector_data = []

            for i, (photo, feature) in enumerate(zip(new_photos, features_batch)):
                if feature is not None and len(feature) > 0:
                    vector_data.append({'url': photo['url'], 'vector': feature})
                else:
                    logging.warning(f"Failed to extract features for photo: {photo['url']}")

                if (i + 1) % 100 == 0:
                    print(f'Processed {i + 1} / {len(new_photos)} photos')

            logging.info(f"Successfully extracted features for {len(vector_data)} out of {len(new_photos)} photos")

        except Exception as e:
            logging.error(f"Error in batch feature extraction: {str(e)}")
            # Fallback to individual processing
            logging.info("Falling back to individual feature extraction...")
            vector_data = []

            for i, p in enumerate(new_photos):
                try:
                    img = load_image_url(p['url'])
                    img_feature = self.feature_service.extract_features(image=img)
                    vector_data.append({'url': p['url'], 'vector': img_feature})
                    print('process ', i)
                except Exception as e:
                    logging.error(f"Error processing photo {p['url']}: {str(e)}")
                    continue

        # Start a client session
        with self.db_client.start_session() as session:
            # Start a transaction
            with session.start_transaction():
                db = session.client[self.db_name]
                collection = db.images
                #insert photo data
                result = collection.insert_many(new_photos)
                logging.info(f"Inserted {len(result.inserted_ids)} images")

                #insert to vector db 
                self.repo_vector.save(vector_data)

    def _get_new_photos(self, photos):
        # Get all existing image URLs in database
        existing_urls = set()
        for img in self.images_collection.find({}, {"url": 1}):
            existing_urls.add(img["url"])
            
        logging.info(f'Existing photos: {len(existing_urls)}')
        logging.info(f'Fetched photos: {len(photos)}')

        images = []
        event_ids = {}
        for i, photo in enumerate(photos):
            user_id = self.users_collection.find_one({"id": photo['query']["id"]}, {"_id": 1})["_id"]
            logging.debug(f"User {user_id} has {len(photo['results'])} photos")
            
            for p in photo["results"]:
                url = self.construct_url(p)
                if url in existing_urls: 
                    continue 
                    
                #cache event_id
                if  p["event_shortname"] in event_ids:
                    event_id = event_ids[p["event_shortname"]]
                else:
                    event = self.events_collection.find_one({"event_shortname": p["event_shortname"]})  
                    event_id = event["_id"]
                    event_ids[p["event_shortname"]] = event_id
                    
                img = {
                    "url": url,
                    "title": p["judul_foto"],
                    "category": p["kategori"],
                    "event_id": event_id,
                    "user_id": user_id, 
                    "id": p.get("foto_id"),
                    "meta_data": p.get("meta_data")
                }
                
                images.append(img)

            cunk = int(0.1 * len(photos))
            if i > 0 and cunk > 0 and i % cunk == 0:
                logging.info(f'Processed images so far: {len(images)}')
        
        logging.info(f'Total new photos found: {len(images)}')
        return images

    def _sync_event(self, events):
        # Insert events
        count = 0    
        for event in events:
            data = {
                "year": event[0],
                "event_shortname": event[1], 
                "event_name": event[2]
            }
            if not self.events_collection.count_documents(data): 
                logging.info(f"Inserting event {data['event_shortname']}")
                count += 1
                self.events_collection.insert_one(data)

        logging.info(f"Inserted {count} new events")

    def _get_unique_events(self, data):
        events = set()
        for photo in data: 
            for p in photo['results']:
                e = (p['tahun'], p['event_shortname'], p['event_name'])
                events.add(e)

        logging.info(f'Found {len(events)} events')
        for event in events:
            logging.debug(f'{event[2]} ({event[1]}) - {event[0]}')
        return events

    def construct_url(self, p):
        if 'url' in p and p['url'].startswith('https://'): return p['url']
        return f"https://simfoni.fpsi.or.id/data/accepted_photo/{p['folder']}/{urllib.parse.quote(p['filename'])}"

    def fix_data(self):
        query = {
        'event_id': ObjectId('64f828058c767d86c4521158'),
        'url': {
            '$regex': '^https://storage.googleapis.com/salon-foto/hpc2022-final'
            }
        }
        images = self.images_collection.find(query)
        for image in images:
            logging.info(f'{image["title"]}: {image["url"]}')

        new_event_id = ObjectId('64f828058c767d86c4521152')
        update_spec = {
            '$set': {
                'event_id': new_event_id
            }
        }
        result = self.images_collection.update_many(query, update_spec)



