from typing import List, Dict, Any, Optional
import logging
import requests
import io
from PIL import Image
from pymongo import MongoClient
from app.core.config import settings
from utils.image_resize import resize_image_to_max_size, should_resize_image, get_image_size_kb
from utils.cloudinary_upload import (
    configure_cloudinary, 
    upload_image_to_cloudinary, 
    is_cloudinary_configured
)


class ThumbnailGeneratorService:
    """
    Service for generating and uploading thumbnails to Cloudinary for images without thumb_url.
    """
    
    def __init__(self, mongo_client: MongoClient):
        self.mongo_client = mongo_client
        self.db = mongo_client['similarity']
        self.images_collection = self.db['images']
        self.logger = logging.getLogger(__name__)
        
        # Configure Cloudinary if credentials are available
        self._configure_cloudinary()
    
    def _configure_cloudinary(self) -> bool:
        """Configure Cloudinary with settings from environment."""
        if not all([
            settings.CLOUDINARY_CLOUD_NAME,
            settings.CLOUDINARY_API_KEY,
            settings.CLOUDINARY_API_SECRET
        ]):
            self.logger.warning("Cloudinary credentials not found in environment variables")
            return False
        
        return configure_cloudinary(
            settings.CLOUDINARY_CLOUD_NAME,
            settings.CLOUDINARY_API_KEY,
            settings.CLOUDINARY_API_SECRET
        )
    
    def get_images_without_thumbnails(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get images from database that don't have thumb_url.
        
        Args:
            limit: Maximum number of images to return
        
        Returns:
            List of image documents without thumb_url
        """
        try:
            # Query for images without thumb_url field or with empty/null thumb_url
            query = {
                "$or": [
                    {"thumb_url": {"$exists": False}},
                    {"thumb_url": None},
                    {"thumb_url": ""}
                ]
            }
            
            cursor = self.images_collection.find(query)
            if limit:
                cursor = cursor.limit(limit)
            
            images = list(cursor)
            self.logger.info(f"Found {len(images)} images without thumbnails")
            return images
            
        except Exception as e:
            self.logger.error(f"Error querying images without thumbnails: {str(e)}")
            return []
    
    def download_image(self, url: str) -> Optional[bytes]:
        """
        Download image from URL.
        
        Args:
            url: Image URL to download
        
        Returns:
            Image bytes if successful, None otherwise
        """
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Validate content type
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                self.logger.warning(f"URL does not point to an image: {url}")
                return None
            
            return response.content
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Failed to download image from {url}: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error downloading image from {url}: {str(e)}")
            return None
    
    def process_single_image(self, image_doc: Dict[str, Any]) -> Optional[str]:
        """
        Process a single image: download, resize if needed, and upload to Cloudinary.
        
        Args:
            image_doc: Image document from database
        
        Returns:
            Cloudinary URL if successful, None otherwise
        """
        url = image_doc.get('url')
        if not url:
            self.logger.warning(f"Image document missing URL: {image_doc.get('_id')}")
            return None
        
        self.logger.info(f"Processing image: {url}")
        
        # Download the image
        image_bytes = self.download_image(url)
        if not image_bytes:
            return None
        
        # Check if image needs resizing
        current_size_kb = get_image_size_kb(image_bytes)
        self.logger.info(f"Original image size: {current_size_kb:.2f} KB")
        
        if should_resize_image(image_bytes, max_size_kb=300):
            self.logger.info("Image needs resizing")
            try:
                # Load image with PIL
                pil_image = Image.open(io.BytesIO(image_bytes))
                
                # Resize the image
                resized_bytes, format_used = resize_image_to_max_size(pil_image, max_size_kb=300)
                final_size_kb = get_image_size_kb(resized_bytes)
                self.logger.info(f"Resized image size: {final_size_kb:.2f} KB")
                
                image_bytes = resized_bytes
                
            except Exception as e:
                self.logger.error(f"Failed to resize image {url}: {str(e)}")
                return None
        else:
            self.logger.info("Image is already under 300KB, skipping resize")
        
        # Upload to Cloudinary
        try:
            upload_result = upload_image_to_cloudinary(
                image_bytes=image_bytes,
                original_url=url,
                folder="simfoni-thumbs",
                tags=["thumbnail", "auto-generated"]
            )
            
            if upload_result and 'secure_url' in upload_result:
                cloudinary_url = upload_result['secure_url']
                self.logger.info(f"Successfully uploaded to Cloudinary: {cloudinary_url}")
                return cloudinary_url
            else:
                self.logger.error(f"Failed to upload image to Cloudinary: {url}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error uploading image to Cloudinary {url}: {str(e)}")
            return None
    
    def update_image_thumbnail_url(self, image_url: str, thumb_url: str) -> bool:
        """
        Update a single image document with the thumbnail URL.
        
        Args:
            image_url: Original image URL
            thumb_url: Cloudinary thumbnail URL
        
        Returns:
            True if update was successful, False otherwise
        """
        try:
            result = self.images_collection.update_one(
                {"url": image_url},
                {"$set": {"thumb_url": thumb_url}}
            )
            
            if result.modified_count > 0:
                self.logger.info(f"Updated thumb_url for image: {image_url}")
                return True
            else:
                self.logger.warning(f"No document updated for image: {image_url}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to update thumb_url for {image_url}: {str(e)}")
            return False
    
    def generate_thumbnails(self, limit: Optional[int] = None, batch_size: int = 10) -> Dict[str, Any]:
        """
        Main function to generate thumbnails for images without thumb_url.
        
        Args:
            limit: Maximum number of images to process
            batch_size: Number of images to process in each batch
        
        Returns:
            Dictionary with processing results and statistics
        """
        if not is_cloudinary_configured():
            return {
                'status': 'error',
                'message': 'Cloudinary is not configured. Please set CLOUDINARY_* environment variables.',
                'details': {
                    'total_processed': 0,
                    'successful': 0,
                    'failed': 0,
                    'failed_details': []
                }
            }
        
        self.logger.info(f"Starting thumbnail generation process (limit: {limit}, batch_size: {batch_size})")
        
        # Get images without thumbnails
        images = self.get_images_without_thumbnails(limit)
        if not images:
            return {
                'status': 'success',
                'message': 'No images found without thumbnails',
                'details': {
                    'total_processed': 0,
                    'successful': 0,
                    'failed': 0,
                    'failed_details': []
                }
            }
        
        total_processed = 0
        successful = 0
        failed = 0
        failed_details = []
        
        # Process images in batches
        for i in range(0, len(images), batch_size):
            batch = images[i:i + batch_size]
            self.logger.info(f"Processing batch {i//batch_size + 1} ({len(batch)} images)")
            
            for image_doc in batch:
                total_processed += 1
                image_url = image_doc.get('url', 'unknown')
                
                try:
                    # Process the image
                    thumb_url = self.process_single_image(image_doc)
                    
                    if thumb_url:
                        # Update database with thumbnail URL
                        if self.update_image_thumbnail_url(image_url, thumb_url):
                            successful += 1
                        else:
                            failed += 1
                            failed_details.append({
                                'url': image_url,
                                'error': 'Failed to update database with thumbnail URL'
                            })
                    else:
                        failed += 1
                        failed_details.append({
                            'url': image_url,
                            'error': 'Failed to process or upload image'
                        })
                        
                except Exception as e:
                    failed += 1
                    failed_details.append({
                        'url': image_url,
                        'error': str(e)
                    })
                    self.logger.error(f"Unexpected error processing image {image_url}: {str(e)}")
        
        # Prepare result
        status = 'success' if failed == 0 else ('partial' if successful > 0 else 'error')
        message = f"Processed {total_processed} images: {successful} successful, {failed} failed"
        
        self.logger.info(f"Thumbnail generation completed: {message}")
        
        return {
            'status': status,
            'message': message,
            'details': {
                'total_processed': total_processed,
                'successful': successful,
                'failed': failed,
                'failed_details': failed_details[:10]  # Limit failed details to first 10
            }
        }
