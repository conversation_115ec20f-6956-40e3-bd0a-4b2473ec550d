from app.services.similarity import Similarity
from models.CompareModel import CompareModel, CompareResponse, CompareResult
from utils.image import load_image_url
from utils.distance import cosine
from infrastructure.persistence.similarity_repository_local_impl import SimilarityRepoLocal
from app.dependencies.dependencies import get_similarity_service

class ImageCompare:
    def __init__(self):
        self.similarity = get_similarity_service()
        print('init compare...')


    def compare(self, body: CompareModel) -> CompareResponse:
        result = []
        main_feature = self.similarity.extract_image_feature(load_image_url(body.url))
        for i, url in enumerate(body.url_compare):
            img = load_image_url(url)
            img_feature = self.similarity.extract_image_feature(img)
            distance = cosine(main_feature, img_feature)   
            result.append(CompareResult(url, distance))
            print(f'{i} -- {distance}')

        return CompareResponse(result)