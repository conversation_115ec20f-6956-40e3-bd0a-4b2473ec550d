
import json
import threading
from models.FileDownloadModel import FileDownloadRequest
import requests
import os
import pathlib
import shutil
import time
from utils.gofile import Gofile
from utils.image import download_file
from utils.progress import generate_id, update_progress
import concurrent.futures


def begin_dowload(body: FileDownloadRequest, task_id: str = None):
    print(f'>>> start processing download files {len(body.urls)} urls....')
    if len(body.urls) == 0:
        return []

    update_progress(task_id, 0.0, prefix='progress_')
    time_start = time.time()

    if body.urls[0].destination_path and os.path.dirname(body.urls[0].destination_path) != '':
        base_path = os.path.dirname(body.urls[0].destination_path) + '_'
    else:
        base_path = 'temp_'

    if task_id == None: base_path += generate_id()
    else: base_path += task_id

    #create base directory
    create_dir(base_path+'/', True)
    print(f'temp dir: {base_path}')

    #downloading all files
    # for i in download_local(body.urls, base_path):
    #     if i%10 == 0: update_progress(task_id, (i/len(body.urls)), prefix='progress_')

    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        create_dir('temp/', True)
        # future_to_url = {executor.submit(download_file, url.url, f'temp/{generate_id()}.jpg'): url.url for url in body.urls}
        future_to_url = {}
        for i, url in enumerate(body.urls):
            file_path = get_file_path(url.url, base_path, url.destination_path)
            create_dir(file_path)   
            future_to_url[executor.submit(download_file, url.url, file_path)] = i

        for future in concurrent.futures.as_completed(future_to_url):
            i = future_to_url[future]
            try:
                data = future.result()
            except Exception as exc:
                print('%r generated an exception: %s' % (i, exc))
            else:            
                print(f'{i} --> {data}')
                update_progress(task_id, (i/len(body.urls)), prefix='progress_')


    # threads = []
    # for i, url in enumerate(body.urls):
    #     print('download at: ', i)        
    #     file_path = get_file_path(url.url, base_path, url.destination_path)
    #     create_dir(file_path)        
    #     t = threading.Thread(target=download_file, args=[url.url, file_path])
    #     t.start()
    #     threads.append(t)

    #     if len(threads) == 10 or i == len(body.urls)-1:
    #         for t in threads: t.join()
    #         threads = []
    #         update_progress(task_id, (i/len(body.urls)), prefix='progress_')

    print(f'zipping directory.... {base_path}')
    #zipping directory
    shutil.make_archive(base_path, 'zip', base_path)

    print(f'uploading file.... {base_path}.zip')
    time_start_upload = time.time()

    #upload in thread....
    file_zip = f'{base_path}.zip'
    results = []
    t = threading.Thread(target=upload, args=[file_zip, results])    
    t.start()

    upload_timeout = 30 * 60 #in second
    while t.is_alive():
        time.sleep(5)
        update_progress(task_id, 0.95, prefix='progress_') 
        if time.time()-time_start_upload >= upload_timeout: 
            update_progress(task_id, json.dumps({'error_message': 'timeout'}), prefix='result_') 
            break

    result = {}
    if len(results) > 0 and len(results[0]) > 0: result['download_page'] = results[0][0]['download_page']

    #uploading files    
    # download_url = Gofile().upload([f'{base_path}.zip'])    
    # result = {'download_url': download_url}

    result['time_elapsed'] = {'upload': time.time() - time_start_upload, 'overall': time.time() - time_start}
    print(f'>> result:  {result}')
    print(f'\n<<FINISH-UPLOAD>> {len(body.urls)} urls took: {time.time() - time_start} \n')     
    update_progress(task_id, 1.0, prefix='progress_')   
    update_progress(task_id, json.dumps(result), prefix='result_') 
    return  result
    

def upload(file_path, result):
    resp = Gofile().upload([file_path])
    result.append(resp)


def get_file_path(url, base_path, destination_path):
    file_name = os.path.basename(url)
    if destination_path:            
        file_name = destination_path
        if os.path.basename(file_name) == '' or pathlib.Path(file_name).suffix == '': 
            file_name = os.path.join(file_name,  os.path.basename(url))
    file_path = os.path.join(base_path, file_name)
    return file_path


def download_local(urls, base_path: str = 'tmp'):
    for i, file in enumerate(urls):          
        file_path = get_file_path(file.url, base_path, file.destination_path)
        print(f'save to : {file_path}')
        create_dir(file_path)

        try: download_file(file.url, file_path)
        except Exception as err: print(f'failed to get: {file.url} -- {err}')

        # response = requests.get(file.url)
        # if response.status_code == 200:
        #     with open(file_path, 'wb') as f:
        #         f.write(response.content)
        # else: print('failed to get: ', file.url)

        # yield i


def create_dir(full_path, clean_dir = False):
    path = os.path.dirname(full_path)    
    print(f'create dir: "{path}" not: "{full_path}"')
    if path == "" : return

    if clean_dir:
        shutil.rmtree(path, ignore_errors=True)

    try:
        os.makedirs(path, exist_ok = True)
    except OSError as error:
        print(f'fail create dir "{path}" --> {error}')                
