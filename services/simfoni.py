import requests
from os import environ
from services.sync_service import SyncService

class Simfoni(SyncService):
    def __init__(self):
        print('init somfoni...')
        self.API_KEY = environ.get('SIMFONI_API_KEY')
        self.BASE_URL = "https://api.simfoni-fpsi.web.id"
        if self.API_KEY is None:
            print('API_KEY is not set')

    def fetchProfiles(self, max = None):
        # Implementation to fetch profiles from Simfoni service
        print("Fetching profiles from Simfoni service")

        profiles = []
        index = 1
        failure = 0

        while True:   
            result = self.get_profile_by_id(f"FP{(index):05d}")
            if len(result) == 0:
                    print('Failed Get profile: ', index) 
                    failure += 1
                    if failure >= 5:
                        print('got only profile: ', len(profiles))
                        break 
                    index += 1
                    continue 
                    
            profiles.append(result)
            index += 1
            failure = 0
            
            if len(profiles) % 100 == 0:
                    print(f'>> Got {len(profiles)} profiles...')
            if max is not None and len(profiles) >= max:
                print(f'-- return max profile: {len(profiles)}')
                return profiles

        return profiles

    def fetchPhotos(self, max = None):
        # Implementation to fetch photos from Simfoni service
        print("Fetching photos from Simfoni service")
        photos = []
        index = 1
        failure = 0

        while True :   
            photo = self.get_photo_by_id(f"FP{(index):05d}")
            if len(photo) == 0:
                print('Failed Get Photo: ', index) 
                failure += 1
                if failure >= 5:
                    print('got only photo: ', len(photos)) 
                    break
                index += 1
                continue 
                
            photos.append(photo)
            index += 1
            failure = 0
            if len(photos) % 100 == 0:
                    print(f'>> Got {len(photos)} photos...')
                    
            #return if max set
            if max is not None and len(photos) >= max:
                print(f'-- return max photo: {len(photos)}')
                return photos

        return photos

    def get_photo_by_id(self, id):
        query = {'id': id}
        header = {'Key': self.API_KEY}
        resp = requests.get(f'{self.BASE_URL}/accepted', params = query, headers = header)
        if resp.status_code == 200:
            return resp.json()['simfoni']
        else:
            print(f'err... {resp.status_code}')
            query = {'id': id}
            return {}

    def get_profile_by_id(self, id):
        query = {'id': id}
        header = {'Key': self.API_KEY}
        resp = requests.get(f'{self.BASE_URL}/member', params = query, headers = header)
        if resp.status_code == 200:
            return resp.json()['simfoni']
        else:
            print(f'err... {resp.status_code}')
            query = {'id': id}
            return {}