# gcr.io/salon-foto/${APP_NAME}/${CI_COMMIT_REF_NAME}
steps:
- name: 'gcr.io/cloud-builders/docker'
  entrypoint: 'bash'
  args: ['-c', 'docker pull gcr.io/$PROJECT_ID/api-similarity:latest || exit 0']
- name: 'gcr.io/cloud-builders/docker'
  args: [
            'build',
            '-t', 'gcr.io/$PROJECT_ID/api-similarity:latest',
            '--cache-from', 'gcr.io/$PROJECT_ID/api-similarity:latest',
            '.'
        ]
images: ['gcr.io/$PROJECT_ID/api-similarity:latest']
