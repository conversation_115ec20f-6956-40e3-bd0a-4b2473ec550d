## Model
Make sure you have required files, put in **model** directory.
Required Files are:     
`model/sfi_tree.ann` : tree build with Annoy    
`model/sfi_url.npz`  : urls of images, index should me match with whats on *sfi_tree.ann* file     
`model/photos.json`  : json file contains information about the photo     
`model/profiles.json`: json file contains user information 


## Installation
- Install [Pipenv](https://pipenv.pypa.io) if not installed     
```sh
pip install --user pipenv
```

- Install all the project dependency using [Pipenv](https://pipenv.pypa.io):

  ```sh
  $ pipenv install --dev
  ```

- Run the application from command prompt:

  ```sh
  $ pipenv run uvicorn main:app --reload
  ```

- You can also open a shell inside virtual environment:

  ```sh
  $ pipenv shell
  ``` 

- Open `localhost:8000/docs` for API Documentation    


### Clean Architecture
Project tree:     
```
project/
    ├── app/
    │   ├── __init__.py
    │   ├── entrypoints/
    │   │   ├── __init__.py
    │   │   └── console.py
    │   └── usecases/
    │       ├── __init__.py
    │       └── user_usecase.py
    ├── domain/
    │   ├── __init__.py
    │   ├── entities/
    │   │   ├── __init__.py
    │   │   └── user.py
    │   └── repositories/
    │       ├── __init__.py
    │       └── user_repository.py
    ├── infrastructure/
    │   ├── __init__.py
    │   ├── persistence/
    │   │   ├── __init__.py
    │   │   └── user_repository_impl.py
    │   └── serializers/
    │       ├── __init__.py
    │       └── user_serializer.py
    ├── requirements.txt
    ├── tests/
    │   ├── __init__.py
    │   ├── app/
    │   │   ├── __init__.py
    │   │   └── usecases/
    │   │       ├── __init__.py
    │   │       └── test_user_usecase.py
    │   ├── domain/
    │   │   ├── __init__.py
    │   │   └── entities/
    │   │       ├── __init__.py
    │   │       └── test_user.py
    │   └── infrastructure/
    │       ├── __init__.py
    │       └── persistence/
    │           ├── __init__.py
    │           └── test_user_repository_impl.py
    └── main.py
```    

Let's go through the different layers and components:     
 1. Domain Layer:
    - domain/entities/user.py: This module defines the User entity, which represents the core business logic of your application.
    - domain/repositories/user_repository.py: This module defines the interface for the UserRepository, which is responsible for persisting and retrieving User entities.

 2. Application Layer:
    - app/usecases/user_usecase.py: This module contains the UserUseCase class, which orchestrates the application's business logic by interacting with the domain layer (entities and repositories).
 3. Infrastructure Layer:   
    - infrastructure/persistence/user_repository_impl.py: This module provides a concrete implementation of the UserRepository interface defined in the domain layer. It handles the actual persistence mechanism (e.g., database, file system, etc.).
    - infrastructure/serializers/user_serializer.py: This module handles the serialization and deserialization of User entities to and from external representations (e.g., JSON, XML, etc.).

 4. Entrypoints:
    - app/entrypoints/console.py: This module contains the code for the console-based entrypoint of your application. It interacts with the application layer (use cases) to handle user input and output.

 5. Tests:
    - tests/domain/entities/test_user.py: This module contains tests for the User entity.
    - tests/app/usecases/test_user_usecase.py: This module contains tests for the UserUseCase.
    - tests/infrastructure/persistence/test_user_repository_impl.py: This module contains tests for the UserRepositoryImpl.

```main.py```: This is the entry point of your application, where you can set up and run the necessary components.
