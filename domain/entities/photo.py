from dataclasses import asdict, dataclass
from typing import Optional, Dict, Any
from pydantic import BaseModel
from typing import List


@dataclass
class Event:
    year: str
    name: str
    short_name: str

@dataclass
class User:
    name: str
    fp_id: str
    email: str
    honors: Optional[str] = None
    club: Optional[str] = None

@dataclass
class Photo:    
    title: str
    category: str
    event: Optional[Event] = None
    user: Optional[User] = None
    meta_data: Optional[Dict[str, Any]] = None
    cluster_id: Optional[Any] = None
    id: Optional[str] = None

    def to_dict(self):
        # Convert the Event and User objects to dictionaries recursively
        event_dict = asdict(self.event) if self.event else None
        user_dict = asdict(self.user) if self.user else None

        # Return the Photo object as a dictionary
        result = {
            "id": self.id,
            "title": self.title,
            "category": self.category,
            "cluster_id": self.cluster_id,
            "event": event_dict,
            "user": user_dict
        }
        
        # Add meta_data if it exists
        if self.meta_data is not None:
            result["meta_data"] = self.meta_data
            
        return result

    @staticmethod
    def to_dict_list(photos: List["Photo"]):
        # Convert a list of Photo objects to a list of dictionaries
        return [photo.to_dict() for photo in photos]