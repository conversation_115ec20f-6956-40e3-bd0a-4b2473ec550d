import abc
from typing import Optional

class VectorCache(abc.ABC):

    @abc.abstractmethod
    def get(self, key: str) -> Optional[str]:
        """
        Get image feature vector from cache
        """
        raise NotImplementedError

    @abc.abstractmethod
    def save(self, key: str, value) -> None:
        """
        Save image feature vector to cache
        """
        raise NotImplementedError