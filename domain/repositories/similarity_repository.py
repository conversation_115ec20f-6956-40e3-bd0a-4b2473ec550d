import abc
from typing import Optional, List, Dict, Any
from domain.entities.photo import Photo

class SimilarityRepository(abc.ABC):
    """
    An abstract class for similarity repository.
    """

    @abc.abstractmethod
    def fetch_image_detail(self, url) -> Optional[Dict[str, Any]]:
        """
        Fetch photo information by url
        """
        raise NotImplementedError

    @abc.abstractmethod
    def fetch_image_by_index(self, index) -> Optional[str]:
        raise NotImplementedError

    @abc.abstractmethod
    async def batch_insert_photos(self, photos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Insert multiple photos in a batch operation.

        Args:
            photos: List of photo data dictionaries to insert

        Returns:
            Dict containing operation results and statistics
        """
        raise NotImplementedError

    @abc.abstractmethod
    def fetch_clusters(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                      sort_by: str = "total_photos", sort_order: str = "desc",
                      limit: Optional[int] = None, max_images_per_cluster: Optional[int] = 100) -> List[Dict[str, Any]]:
        """
        Fetch cluster data grouped by cluster_id.

        Args:
            min_photos: Minimum number of photos per cluster
            max_photos: Maximum number of photos per cluster
            sort_by: Field to sort by (default: total_photos)
            sort_order: Sort order - 'asc' or 'desc' (default: desc)
            limit: Maximum number of clusters to return

        Returns:
            List of cluster data dictionaries
        """
        raise NotImplementedError

    @abc.abstractmethod
    def find_image_cluster(self, image_url: str) -> Optional[Dict[str, Any]]:
        """
        Find which cluster an image belongs to.

        Args:
            image_url: URL of the image to find

        Returns:
            Dictionary containing cluster_id and cluster data, or None if not found
        """
        raise NotImplementedError

    @abc.abstractmethod
    def fetch_clusters_simple(self, min_photos: Optional[int] = None, max_photos: Optional[int] = None,
                             sort_by: str = "total_photos", sort_order: str = "desc",
                             limit: Optional[int] = None, max_images_per_cluster: Optional[int] = 100) -> List[Dict[str, Any]]:
        """
        Fetch cluster data using simple queries instead of aggregation.
        This method reduces MongoDB memory usage by doing the heavy lifting in Python.

        Args:
            min_photos: Minimum number of photos per cluster
            max_photos: Maximum number of photos per cluster
            sort_by: Field to sort by (default: total_photos)
            sort_order: Sort order - 'asc' or 'desc' (default: desc)
            limit: Maximum number of clusters to return
            max_images_per_cluster: Maximum number of images to return per cluster

        Returns:
            List of cluster data dictionaries
        """
        raise NotImplementedError

    @abc.abstractmethod
    def find_one_without_cluster_id(self) -> Optional[Dict[str, Any]]:
        """
        Find one document that doesn't have a cluster_id assigned.

        Returns:
            Dictionary containing photo data without cluster_id, or None if all photos have cluster_id
        """
        raise NotImplementedError

    @abc.abstractmethod
    def get_max_cluster_id(self) -> int:
        """
        Get the maximum cluster_id currently in the database.

        Returns:
            Maximum cluster_id as integer, or 0 if no clusters exist
        """
        raise NotImplementedError

    @abc.abstractmethod
    def update_cluster_id(self, url: str, cluster_id: int) -> bool:
        """
        Update the cluster_id for a specific image URL.

        Args:
            url: Image URL to update
            cluster_id: New cluster_id to assign

        Returns:
            True if update was successful, False otherwise
        """
        raise NotImplementedError

    @abc.abstractmethod
    def fetch_images_paginated(self, page: int = 1, page_size: int = 20,
                             category: Optional[str] = None,
                             event_id: Optional[str] = None,
                             user_id: Optional[str] = None,
                             cluster_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Fetch images with pagination and optional filters.

        Args:
            page: Page number (1-based)
            page_size: Number of items per page
            category: Filter by category
            event_id: Filter by event ID
            user_id: Filter by user ID
            cluster_id: Filter by cluster ID

        Returns:
            Dictionary containing paginated results with metadata
        """
        raise NotImplementedError
