import abc
from typing import Optional, Union
import numpy as np

class SimilarityUseCase(abc.ABC):
    """
    Interface for the Similarity Use Case.
    """

    @abc.abstractmethod
    def extract_image_feature(self, image: Optional[np.ndarray] = None, image_url: Optional[str] = None) -> np.ndarray:
        """
        Extracts image features from the given image or URL.
        
        Args:
            image: Optional numpy array representing the image. If not provided, image_url must be provided.
            image_url: Optional URL of the image. If image is not provided, this will be used to download the image.
            
        Returns:
            A numpy array containing the extracted features
            
        Raises:
            ValueError: If both image and image_url are None
        """
        raise NotImplementedError