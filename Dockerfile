# ┌───────────────────────────────────────────────────────────┐
# │                       BUILDER STAGE                       │
# └───────────────────────────────────────────────────────────┘
FROM python:3.12-slim AS builder

# 1) Install build tools, then clean apt caches
RUN apt-get update \
 && apt-get install -y --no-install-recommends gcc g++ \
 && rm -rf /var/lib/apt/lists/*

# 2) Upgrade pip & install pipenv
RUN pip install --upgrade pip pipenv

WORKDIR /app

# 3) Copy only Pipfile + lock for fastest rebuilds
COPY Pipfile Pipfile.lock ./

# 4) Install *all* deps system-wide (no virtualenv),
#    including transitive ones like certifi
RUN pipenv install --deploy --system --ignore-pipfile \
 && rm -rf /root/.cache/pip

# ┌───────────────────────────────────────────────────────────┐
# │                        FINAL STAGE                        │
# └───────────────────────────────────────────────────────────┘
FROM python:3.12-slim

# 5) Install runtime OS deps + clean apt caches
# RUN apt-get update \
#  && apt-get install -y --no-install-recommends \
#       ffmpeg libsm6 libxext6 \
#  && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 6) Copy the Python interpreter + site-packages from builder
COPY --from=builder /usr/local /usr/local

# 7) Copy your application code
COPY . .

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
