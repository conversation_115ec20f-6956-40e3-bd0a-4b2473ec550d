variables:
  GOOGLE_IMAGE_NAME: gcr.io/salon-foto/${APP_NAME}/${CI_COMMIT_REF_NAME}
  APP_NAME: api-similarity
  CLOUD_RUN_REGION: asia-southeast1

cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - /root/.cache/pip
    - /usr/src/app/.venv
    - /usr/src/app/.pipenv

.buildImage:
  stage: build
  image: google/cloud-sdk:alpine
  script:
    - rm .gitignore
    - cat $ENV > .env
    - cat $GCLOUD_RUN_KEY > ${HOME}/gcloud-service-key.json
    - gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
    - gcloud config set project $GOOGLE_PROJECT_ID
    - gcloud config set gcloudignore/enabled false
    - gcloud builds submit --tag $GOOGLE_IMAGE_NAME .

.cloudRun:
  stage: build
  image: google/cloud-sdk:alpine
  script:
    - rm .gitignore
    - mkdir -p model
    # - wget -O model.zip $MODEL_URL && unzip -d model model.zip
    # - ls -al model
    # - ls -alh model/photos.json
    - cat $GCLOUD_RUN_KEY > ${HOME}/gcloud-service-key.json
    - gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
    - gcloud config set project $GOOGLE_PROJECT_ID
    - gcloud config set gcloudignore/enabled false
    # - gcloud builds submit --tag $GOOGLE_IMAGE_NAME .
    # - gcloud builds submit --config cloudbuild.yaml .
    - gcloud run deploy $APP_NAME --port 8000 --source . --platform managed --region $CLOUD_RUN_REGION --allow-unauthenticated
  tags:
    - production

deployCloudRunDevSaas:
  extends: .cloudRun
  when: manual
  variables:
    APP_NAME: api-similarity-dev
  only:
    - dev
  tags:
    - saas-linux-medium-amd64

deployCloudRunDev:
  extends: .cloudRun
  when: manual
  variables:
    APP_NAME: api-similarity-dev
  only:
    - dev
  tags:
    - testing-docker

deployCloudRun:
  extends: .cloudRun
  when: manual
  only:
    - main

buildCloudImage:
  extends: .buildImage
  when: manual
  only:
    - main
    - dev
