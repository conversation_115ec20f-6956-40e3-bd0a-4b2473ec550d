#!/usr/bin/env python3
"""
Test script for thumbnail generation functionality.
This script tests the individual components without requiring a full server setup.
"""

import os
import sys
import logging
from PIL import Image
import io
import requests

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.image_resize import resize_image_to_max_size, should_resize_image, get_image_size_kb
from utils.cloudinary_upload import configure_cloudinary, is_cloudinary_configured

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_image_resize():
    """Test the image resizing functionality."""
    logger.info("Testing image resize functionality...")
    
    try:
        # Create a test image (large enough to need resizing)
        test_image = Image.new('RGB', (2000, 2000), color='red')
        
        # Convert to bytes
        buffer = io.BytesIO()
        test_image.save(buffer, format='JPEG', quality=95)
        original_bytes = buffer.getvalue()
        
        original_size_kb = get_image_size_kb(original_bytes)
        logger.info(f"Original test image size: {original_size_kb:.2f} KB")
        
        # Test if it needs resizing
        needs_resize = should_resize_image(original_bytes, max_size_kb=300)
        logger.info(f"Image needs resizing: {needs_resize}")
        
        if needs_resize:
            # Test resizing
            resized_bytes, format_used = resize_image_to_max_size(test_image, max_size_kb=300)
            resized_size_kb = get_image_size_kb(resized_bytes)
            logger.info(f"Resized image size: {resized_size_kb:.2f} KB, format: {format_used}")
            
            if resized_size_kb <= 300:
                logger.info("✅ Image resize test PASSED")
                return True
            else:
                logger.error("❌ Image resize test FAILED - size still too large")
                return False
        else:
            logger.info("✅ Image resize test PASSED - no resize needed")
            return True
            
    except Exception as e:
        logger.error(f"❌ Image resize test FAILED: {str(e)}")
        return False


def test_cloudinary_config():
    """Test Cloudinary configuration."""
    logger.info("Testing Cloudinary configuration...")
    
    try:
        # Check if environment variables are set
        cloud_name = os.getenv('CLOUDINARY_CLOUD_NAME')
        api_key = os.getenv('CLOUDINARY_API_KEY')
        api_secret = os.getenv('CLOUDINARY_API_SECRET')
        
        if not all([cloud_name, api_key, api_secret]):
            logger.warning("⚠️  Cloudinary environment variables not set - this is expected for testing")
            logger.info("To test Cloudinary functionality, set these environment variables:")
            logger.info("- CLOUDINARY_CLOUD_NAME")
            logger.info("- CLOUDINARY_API_KEY")
            logger.info("- CLOUDINARY_API_SECRET")
            return True  # Not a failure, just not configured
        
        # Test configuration
        config_success = configure_cloudinary(cloud_name, api_key, api_secret)
        if config_success:
            logger.info("✅ Cloudinary configuration test PASSED")
            return True
        else:
            logger.error("❌ Cloudinary configuration test FAILED")
            return False
            
    except Exception as e:
        logger.error(f"❌ Cloudinary configuration test FAILED: {str(e)}")
        return False


def test_image_download():
    """Test image downloading functionality."""
    logger.info("Testing image download functionality...")
    
    try:
        # Use a small test image URL (placeholder service)
        test_url = "https://via.placeholder.com/150x150.jpg"
        
        response = requests.get(test_url, timeout=10)
        response.raise_for_status()
        
        if response.headers.get('content-type', '').startswith('image/'):
            image_bytes = response.content
            size_kb = get_image_size_kb(image_bytes)
            logger.info(f"Downloaded test image: {size_kb:.2f} KB")
            
            # Verify it's a valid image
            Image.open(io.BytesIO(image_bytes))
            logger.info("✅ Image download test PASSED")
            return True
        else:
            logger.error("❌ Image download test FAILED - not an image")
            return False
            
    except Exception as e:
        logger.error(f"❌ Image download test FAILED: {str(e)}")
        return False


def test_database_connection():
    """Test database connection (if MongoDB is available)."""
    logger.info("Testing database connection...")
    
    try:
        from configs.db import get_db_mongo
        
        mongo_client = get_db_mongo()
        db = mongo_client['similarity']
        
        # Test connection with a simple ping
        mongo_client.admin.command('ping')
        
        # Test accessing the images collection
        images_collection = db['images']
        count = images_collection.count_documents({})
        logger.info(f"Found {count} images in database")
        
        logger.info("✅ Database connection test PASSED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database connection test FAILED: {str(e)}")
        logger.info("This is expected if MongoDB is not running or not configured")
        return False


def main():
    """Run all tests."""
    logger.info("Starting thumbnail generation tests...")
    logger.info("=" * 50)
    
    tests = [
        ("Image Resize", test_image_resize),
        ("Cloudinary Config", test_cloudinary_config),
        ("Image Download", test_image_download),
        ("Database Connection", test_database_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY:")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        emoji = "✅" if result else "❌"
        logger.info(f"{emoji} {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        logger.info("🎉 All tests passed! The thumbnail generation system is ready to use.")
    else:
        logger.info("⚠️  Some tests failed. Check the logs above for details.")
    
    return passed == len(tests)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
