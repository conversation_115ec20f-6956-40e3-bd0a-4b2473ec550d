steps:
- name: 'gcr.io/cloud-builders/docker'
  entrypoint: 'bash'
  args: ['-c', 'docker pull ${_IMAGE_NAME}:latest || exit 0']
- name: 'gcr.io/cloud-builders/docker'
  args: [
            'build',
            '-t', '${_IMAGE_NAME}:latest',
            '--cache-from', '${_IMAGE_NAME}:latest',
            '.'
        ]
images: ['${_IMAGE_NAME}:latest']
substitutions:
    _IMAGE_NAME: 'gcr.io/${PROJECT_ID}/api-similarity'
options:
    dynamic_substitutions: true
