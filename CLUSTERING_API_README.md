# Clustering API Documentation

## Overview

The Clustering API provides endpoints to fetch and analyze image cluster data. Images are grouped into clusters based on their visual similarity using machine learning clustering algorithms. Each cluster contains images that are visually similar to each other.

## Base URL

```
/v1/clusters
```

## Authentication

All endpoints require a `Public-Key` header for authentication.

## Endpoints

### 1. Get All Clusters

**Endpoint:** `GET /v1/clusters/`

**Description:** Returns all cluster data grouped by cluster ID. Each cluster contains a list of images that belong to that cluster.

**Parameters:**

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `min_photos` | integer | No | Minimum number of photos per cluster | `4` |
| `max_photos` | integer | No | Maximum number of photos per cluster | `10` |
| `sort_by` | string | No | Sort by field (currently supports 'total_photos') | `total_photos` |
| `sort_order` | string | No | Sort order: 'asc' or 'desc' | `desc` |
| `limit` | integer | No | Maximum number of clusters to return (1-1000) | `50` |
| `max_images_per_cluster` | integer | No | Maximum number of images to return per cluster (1-1000, default: 100) | `100` |

**Example Requests:**

```bash
# Get all clusters
GET /v1/clusters/

# Get clusters with at least 4 photos
GET /v1/clusters/?min_photos=4

# Get top 10 clusters by photo count
GET /v1/clusters/?sort_by=total_photos&sort_order=desc&limit=10

# Get clusters with 3-5 photos, sorted ascending
GET /v1/clusters/?min_photos=3&max_photos=5&sort_order=asc

# Get clusters with limited images per cluster for better performance
GET /v1/clusters/?max_images_per_cluster=50
```

**Response Format:**

```json
{
  "message": "success",
  "result": [
    {
      "cluster_id": 55,
      "total_images": 5,
      "images": [
        {
          "url": "https://example.com/photo1.jpg",
          "title": "Sunset Beach",
          "category": "Nature",
          "id": "img_001",
          "event": {
            "year": "2024",
            "name": "Photo Contest",
            "short_name": "PC2024"
          },
          "user": {
            "name": "John Doe",
            "email": "<EMAIL>",
            "honors": "EFIAP",
            "club": "Photo Club",
            "fp_id": "fp001"
          },
          "meta_data": {
            "camera": "Canon EOS R5",
            "location": "Bali"
          }
        }
      ]
    }
  ],
  "total_clusters": 1,
  "error": false
}
```

### 2. Find Cluster by Image

**Endpoint:** `GET /v1/clusters/find-by-image`

**Description:** Find which cluster a specific image belongs to and return all images in that cluster.

**Parameters:**

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `image_url` | string | Yes | URL of the image to find cluster for | `https://example.com/photo.jpg` |

**Example Request:**

```bash
GET /v1/clusters/find-by-image?image_url=https://example.com/photo1.jpg
```

**Response Format:**

```json
{
  "message": "success",
  "cluster_id": 55,
  "cluster_data": {
    "cluster_id": 55,
    "total_images": 5,
    "images": [
      {
        "url": "https://example.com/photo1.jpg",
        "title": "Sunset Beach",
        "category": "Nature",
        "id": "img_001",
        "event": {
          "year": "2024",
          "name": "Photo Contest",
          "short_name": "PC2024"
        },
        "user": {
          "name": "John Doe",
          "email": "<EMAIL>",
          "honors": "EFIAP",
          "club": "Photo Club",
          "fp_id": "fp001"
        },
        "meta_data": {
          "camera": "Canon EOS R5",
          "location": "Bali"
        }
      }
    ]
  },
  "error": false
}
```

**Response when image not found:**

```json
{
  "message": "Image not found or not assigned to any cluster",
  "cluster_id": null,
  "cluster_data": null,
  "error": false
}
```

## Data Structure

### ClusterData Object

| Field | Type | Description |
|-------|------|-------------|
| `cluster_id` | integer | Unique identifier for the cluster |
| `total_images` | integer | Total number of images in the cluster |
| `images` | array | Array of ClusterImage objects |

### ClusterImage Object

| Field | Type | Description |
|-------|------|-------------|
| `url` | string | URL of the image |
| `title` | string | Title of the image (optional) |
| `category` | string | Category of the image (optional) |
| `id` | string | Unique identifier for the image (optional) |
| `event` | object | Event information (optional) |
| `user` | object | User/photographer information (optional) |
| `meta_data` | object | Additional metadata (optional) |

### Event Object

| Field | Type | Description |
|-------|------|-------------|
| `year` | string | Year of the event |
| `name` | string | Full name of the event |
| `short_name` | string | Short name of the event |

### User Object

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | Full name of the photographer |
| `email` | string | Email address |
| `honors` | string | Photography honors/distinctions (optional) |
| `club` | string | Photography club affiliation (optional) |
| `fp_id` | string | Photographer ID (optional) |

## Error Responses

### 400 Bad Request

```json
{
  "detail": "min_photos cannot be greater than max_photos"
}
```

### 500 Internal Server Error

```json
{
  "message": "Error fetching clusters: [error details]",
  "result": [],
  "total_clusters": 0,
  "error": true
}
```

## Use Cases

### 1. Find Similar Images
Use the clustering API to find images that are visually similar to a given image:

```bash
GET /v1/clusters/find-by-image?image_url=https://example.com/target-image.jpg
```

### 2. Quality Control
Find clusters with many similar images to identify potential duplicates:

```bash
GET /v1/clusters/?min_photos=10&sort_by=total_photos&sort_order=desc
```

### 3. Content Discovery
Browse clusters to discover related content:

```bash
GET /v1/clusters/?sort_by=total_photos&sort_order=desc&limit=20
```

### 4. Data Analysis
Analyze clustering patterns by filtering and sorting:

```bash
GET /v1/clusters/?min_photos=5&max_photos=15&sort_order=asc
```

## Implementation Notes

- Clusters are generated using machine learning algorithms that analyze image features
- The `cluster_id` is assigned automatically during the clustering process
- Images without cluster assignments (cluster_id = null) are not included in results
- The API supports efficient filtering and sorting at the database level
- All responses include pagination-friendly metadata (total_clusters)

## Performance Optimization

For large datasets, the API uses a **simple query approach** instead of heavy MongoDB aggregation to prevent memory issues:

- **Simple Queries**: Uses multiple lightweight MongoDB queries instead of memory-intensive aggregation
- **Python Processing**: Heavy lifting is done in Python rather than MongoDB
- **max_images_per_cluster**: Limits the number of images returned per cluster (default: 100)
- **Memory Distribution**: Spreads memory usage across multiple small operations

### Recommended Usage for Large Datasets

```bash
# For large datasets, limit images per cluster
GET /v1/clusters/?max_images_per_cluster=50&limit=20

# Use filtering to reduce data size
GET /v1/clusters/?min_photos=5&max_photos=20&max_images_per_cluster=30
```

### Memory Error Prevention

The new implementation prevents MongoDB memory errors by:

1. **Avoiding Heavy Aggregation**: No more `$group` operations that cause memory overflow
2. **Simple Query Pattern**:
   - Get distinct cluster IDs (lightweight)
   - For each cluster: count + fetch limited images
   - Process and sort in Python
3. **Configurable Limits**: Use `max_images_per_cluster` to control memory usage
4. **Efficient Filtering**: Apply filters early to reduce data processing

### Migration from Previous Version

The API automatically uses the new memory-efficient approach. No changes needed in your existing code!

## Rate Limiting

Please refer to the main API documentation for rate limiting information.

## Support

For technical support or questions about the Clustering API, please contact the development team.
