from os import environ
import redis
from pymongo.mongo_client import MongoClient
from pymongo.server_api import Server<PERSON><PERSON>

def get_db_redis():
    return redis.Redis(
        host= environ.get('REDIS_HOST'),
        port=environ.get('REDIS_PORT'), 
        password=environ.get('REDIS_PASSWORD'))

def get_db_mongo():
    uri = environ.get('MONGODB_URI')
    if uri is None or uri == "":    
        raise ValueError("You must set your 'MONGODB_URI' environmental variable. See\n\t https://www.mongodb.com/docs/drivers/python/current/usage-examples/#environment-variable")

    # Create a new client and connect to the server
    client = MongoClient(uri, server_api=ServerApi('1'))

    # Send a ping to confirm a successful connection
    try:
        print('try to ping mongodb...')
        client.admin.command('ping')
        print("Pinged your deployment. You successfully connected to MongoDB!")
        # print('type of: ', client.similarity)
        return client
    except Exception as e:
        print(e)