from PIL import Image 
import numpy as np
import requests
import shutil
# import cv2
import logging
import io


def load_image_url_rgb(url, required_size = (224,224)):
    img = Image.open(requests.get(url, stream=True).raw)
    img = Image.fromarray(np.array(img))
    img = img.resize(required_size)
    img = (np.expand_dims(np.array(img), 0)/255).astype(np.float32)
    return img

def load_image_url(img_url, required_size=(224, 224)):
    try:
        # Add timeout and validate response
        response = requests.get(img_url, stream=True, timeout=10)
        if response.status_code != 200:
            return {'error': f'Failed to fetch image. Status code: {response.status_code}'}
            
        # Validate content type
        content_type = response.headers.get('content-type', '')
        if not content_type.startswith('image/'):
            return {'error': f'URL does not point to an image. Content-Type: {content_type}'}

        img = Image.open(response.raw)
        
        # Validate image mode and convert if necessary
        if img.mode not in ['RGB', 'L']:
            img = img.convert('RGB')
            
        # Convert grayscale to RGB if needed
        if img.mode == 'L':
            logging.info(f'Converting grayscale image to RGB: {img_url}')
            img = img.convert('RGB')
            
        img = img.resize(required_size)
        img_array = np.array(img)

        img_array = (np.expand_dims(img_array, 0)/255).astype(np.float32)
        return img_array
        
    except requests.exceptions.Timeout:
        logging.error(f'Timeout while fetching image: {img_url}')
        return {'error': 'Request timed out while fetching image'}
    except requests.exceptions.RequestException as e:
        logging.error(f'Network error while fetching image: {img_url}, error: {str(e)}')
        return {'error': f'Network error: {str(e)}'}
    except Exception as e:
        logging.error(f'Error loading image from URL: {img_url}, error: {str(e)}')
        return {'error': f'Image processing error: {str(e)}'}

# Helper function to load images from URLs or local paths
def load_images(image_sources):
    """Loads images from a list of URLs or local file paths."""
    pil_images = []
    for src in image_sources:
        try:
            if src.startswith('http://') or src.startswith('https://'):
                response = requests.get(src, timeout=10)
                response.raise_for_status() # Raise an exception for bad status codes
                img = Image.open(io.BytesIO(response.content)).convert("RGB")
            else: # It's a local path
                img = Image.open(src).convert("RGB")
            pil_images.append(img)
        except Exception as e:
            print(f"Could not load image {src}: {e}")
            # Optionally, add a placeholder image
            pil_images.append(Image.new('RGB', (224, 224), 'gray'))
    return pil_images

#Deprecated, we no longer use cv2
def load_image_url_v1(img_url, required_size = (224,224)):
    try:
        # Add timeout and validate response
        response = requests.get(img_url, stream=True, timeout=10)
        if response.status_code != 200:
            return {'error': f'Failed to fetch image. Status code: {response.status_code}'}
            
        # Validate content type
        content_type = response.headers.get('content-type', '')
        if not content_type.startswith('image/'):
            return {'error': f'URL does not point to an image. Content-Type: {content_type}'}

        img = Image.open(response.raw)
        
        # Validate image mode and convert if necessary
        if img.mode not in ['RGB', 'L']:
            img = img.convert('RGB')
            
        img = Image.fromarray(np.array(img))
        img = img.resize(required_size)
        img = np.array(img)

        #if only 2 channel (Grey image), convert to 3 channel
        if len(list(img.shape)) == 2:
            logging.info(f'Converting grayscale image to RGB: {img_url}')
            # img = cv2.cvtColor(np.squeeze(img), cv2.COLOR_GRAY2RGB)

        img = (np.expand_dims(img, 0)/255).astype(np.float32)
        return img
        
    except requests.exceptions.Timeout:
        logging.error(f'Timeout while fetching image: {img_url}')
        return {'error': 'Request timed out while fetching image'}
    except requests.exceptions.RequestException as e:
        logging.error(f'Network error while fetching image: {img_url}, error: {str(e)}')
        return {'error': f'Network error: {str(e)}'}
    # except PIL.UnidentifiedImageError:
    #     logging.error(f'Invalid or corrupted image data: {img_url}')
    #     return {'error': 'Invalid or corrupted image data'}
    except Exception as e:
        logging.error(f'Error loading image from URL: {img_url}, error: {str(e)}')
        return {'error': f'Image processing error: {str(e)}'}


def download_file(url, local_filename: str = None):
    if local_filename is None:
        local_filename = url.split('/')[-1]

    with requests.get(url, stream=True) as r:
        with open(local_filename, 'wb') as f:
            shutil.copyfileobj(r.raw, f)

    return local_filename


def download_file_url(url, file_path):
    response = requests.get(url)
    if response.status_code == 200:
        with open(file_path, 'wb') as f:
            f.write(response.content)
        return True
    else: print('failed to get: ', url)  
    return False  
