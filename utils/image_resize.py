from PIL import Image
import io
import logging
from typing import <PERSON>ple, Optional


def resize_image_to_max_size(image: Image.Image, max_size_kb: int = 300, quality_start: int = 95, quality_min: int = 20) -> Tuple[bytes, str]:
    """
    Resize an image to ensure it's under the specified maximum file size while maintaining good quality.
    
    Args:
        image: PIL Image object to resize
        max_size_kb: Maximum file size in KB (default: 300)
        quality_start: Starting JPEG quality (default: 95)
        quality_min: Minimum JPEG quality to try (default: 20)
    
    Returns:
        Tuple of (image_bytes, format) where format is the image format used
    
    Raises:
        ValueError: If image cannot be compressed to target size
    """
    max_size_bytes = max_size_kb * 1024
    
    # Convert to RGB if necessary (for JPEG compatibility)
    if image.mode in ('RGBA', 'LA', 'P'):
        # Create a white background for transparent images
        background = Image.new('RGB', image.size, (255, 255, 255))
        if image.mode == 'P':
            image = image.convert('RGBA')
        background.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
        image = background
    elif image.mode != 'RGB':
        image = image.convert('RGB')
    
    # First, try with original size and decreasing quality
    quality = quality_start
    while quality >= quality_min:
        buffer = io.BytesIO()
        image.save(buffer, format='JPEG', quality=quality, optimize=True)
        size = buffer.tell()
        
        if size <= max_size_bytes:
            logging.info(f"Image compressed to {size} bytes with quality {quality}")
            return buffer.getvalue(), 'JPEG'
        
        quality -= 5
    
    # If still too large, try reducing dimensions
    original_width, original_height = image.size
    scale_factor = 0.9
    
    while scale_factor > 0.3:  # Don't go below 30% of original size
        new_width = int(original_width * scale_factor)
        new_height = int(original_height * scale_factor)
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Try different qualities with the resized image
        quality = quality_start
        while quality >= quality_min:
            buffer = io.BytesIO()
            resized_image.save(buffer, format='JPEG', quality=quality, optimize=True)
            size = buffer.tell()
            
            if size <= max_size_bytes:
                logging.info(f"Image resized to {new_width}x{new_height} and compressed to {size} bytes with quality {quality}")
                return buffer.getvalue(), 'JPEG'
            
            quality -= 5
        
        scale_factor -= 0.1
    
    # If we still can't get it small enough, return the smallest version we can make
    new_width = int(original_width * 0.3)
    new_height = int(original_height * 0.3)
    resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    buffer = io.BytesIO()
    resized_image.save(buffer, format='JPEG', quality=quality_min, optimize=True)
    final_size = buffer.tell()
    
    logging.warning(f"Could not compress image below {max_size_kb}KB. Final size: {final_size} bytes")
    return buffer.getvalue(), 'JPEG'


def get_image_size_kb(image_bytes: bytes) -> float:
    """
    Get the size of image bytes in KB.
    
    Args:
        image_bytes: Image data as bytes
    
    Returns:
        Size in KB as float
    """
    return len(image_bytes) / 1024


def should_resize_image(image_bytes: bytes, max_size_kb: int = 300) -> bool:
    """
    Check if an image needs to be resized based on its current size.
    
    Args:
        image_bytes: Image data as bytes
        max_size_kb: Maximum allowed size in KB
    
    Returns:
        True if image needs resizing, False otherwise
    """
    current_size_kb = get_image_size_kb(image_bytes)
    return current_size_kb > max_size_kb
