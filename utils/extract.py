import urllib.parse

def get_base_url(url):
    link = urllib.parse.urlsplit(url)
    return f'{link.scheme}://{link.netloc}'


def url_only(url):
  """
  Removes all URL parameters from a given URL.

  Args:
    url: The URL to remove parameters from.

  Returns:
    The URL without parameters.
  """

  parsed_url = urllib.parse.urlparse(url)
  return urllib.parse.urlunparse((
    parsed_url.scheme,
    parsed_url.netloc,
    parsed_url.path,
    None,  # Remove query parameters
    None,  # Remove fragment
    parsed_url.fragment,
  ))

