import numpy as np
import torch


def convert_numpy_types(obj):
        """Recursively convert numpy types to native Python types for JSON serialization."""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return convert_numpy_types(obj.tolist())
        elif isinstance(obj, dict):
            return {key: convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        return obj


def ensure_float32(x):
    """
    Ensure that `x` is a NumPy array of dtype float32.
    - If it’s a torch.Tensor, it will be detached, moved to CPU, converted to numpy.
    - If it’s a numpy.ndarray of dtype float64, it will be astyped to float32.
    - If it’s any other numpy dtype, it’s returned as-is.
    
    Raises:
      TypeError if input is neither numpy.ndarray nor torch.Tensor.
    """
    # 1) Handle PyTorch tensors
    if isinstance(x, torch.Tensor):
        # detach so no gradients, move to CPU, convert to numpy
        arr = x.detach().cpu().numpy()
    elif isinstance(x, np.ndarray):
        arr = x
    else:
        raise TypeError(f"Expected numpy.ndarray or torch.Tensor, got {type(x)}")

    # 2) Ensure dtype is float32
    if arr.dtype == np.float64:
        arr = arr.astype(np.float32)
    # (you could also enforce float32 only, e.g. raise for int dtypes)
    return arr   