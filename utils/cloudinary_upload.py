import cloudinary
import cloudinary.uploader
import cloudinary.api
from typing import Optional, Dict, Any
import logging
import os
from urllib.parse import urlparse
import hashlib


def configure_cloudinary(cloud_name: str, api_key: str, api_secret: str) -> bool:
    """
    Configure Cloudinary with the provided credentials.
    
    Args:
        cloud_name: Cloudinary cloud name
        api_key: Cloudinary API key
        api_secret: Cloudinary API secret
    
    Returns:
        True if configuration is successful, False otherwise
    """
    try:
        cloudinary.config(
            cloud_name=cloud_name,
            api_key=api_key,
            api_secret=api_secret,
            secure=True
        )
        
        # Test the configuration by making a simple API call
        cloudinary.api.ping()
        logging.info("Cloudinary configuration successful")
        return True
    except Exception as e:
        logging.error(f"Failed to configure Cloudinary: {str(e)}")
        return False


def is_cloudinary_configured() -> bool:
    """
    Check if Cloudinary is properly configured.
    
    Returns:
        True if Cloudinary is configured, False otherwise
    """
    config = cloudinary.config()
    return bool(config.cloud_name and config.api_key and config.api_secret)


def generate_public_id_from_url(original_url: str) -> str:
    """
    Generate a unique public ID for Cloudinary based on the original URL.
    
    Args:
        original_url: The original image URL
    
    Returns:
        A unique public ID for Cloudinary
    """
    # Create a hash of the URL to ensure uniqueness
    url_hash = hashlib.md5(original_url.encode()).hexdigest()[:12]
    
    # Extract filename from URL if possible
    parsed_url = urlparse(original_url)
    filename = os.path.basename(parsed_url.path)
    
    # Remove extension from filename
    if '.' in filename:
        filename = filename.rsplit('.', 1)[0]
    
    # Clean filename to be Cloudinary-compatible
    filename = ''.join(c for c in filename if c.isalnum() or c in '-_')
    
    # Combine filename with hash
    if filename:
        return f"{filename}_{url_hash}"
    else:
        return f"image_{url_hash}"


def upload_image_to_cloudinary(
    image_bytes: bytes,
    original_url: str,
    folder: str = "simfoni-thumbs",
    public_id: Optional[str] = None,
    tags: Optional[list] = None
) -> Optional[Dict[str, Any]]:
    """
    Upload an image to Cloudinary.
    
    Args:
        image_bytes: Image data as bytes
        original_url: Original URL of the image (for generating public_id)
        folder: Cloudinary folder to upload to (default: "simfoni-thumbs")
        public_id: Custom public ID (if None, will be generated from URL)
        tags: List of tags to add to the image
    
    Returns:
        Cloudinary upload response dict if successful, None if failed
    """
    if not is_cloudinary_configured():
        logging.error("Cloudinary is not configured. Please set CLOUDINARY_* environment variables.")
        return None
    
    try:
        # Generate public ID if not provided
        if not public_id:
            public_id = generate_public_id_from_url(original_url)
        
        # Prepare upload options
        upload_options = {
            'folder': folder,
            'public_id': public_id,
            'resource_type': 'image',
            'overwrite': True,  # Overwrite if exists
            'unique_filename': False,  # Use our custom public_id
            'use_filename': False,
        }
        
        # Add tags if provided
        if tags:
            upload_options['tags'] = tags
        
        # Add context with original URL
        upload_options['context'] = f"original_url={original_url}"
        
        # Upload the image
        result = cloudinary.uploader.upload(image_bytes, **upload_options)
        
        logging.info(f"Successfully uploaded image to Cloudinary: {result.get('secure_url')}")
        return result
        
    except Exception as e:
        logging.error(f"Failed to upload image to Cloudinary: {str(e)}")
        return None


def delete_image_from_cloudinary(public_id: str, folder: str = "simfoni-thumbs") -> bool:
    """
    Delete an image from Cloudinary.
    
    Args:
        public_id: Public ID of the image to delete
        folder: Cloudinary folder the image is in
    
    Returns:
        True if deletion was successful, False otherwise
    """
    if not is_cloudinary_configured():
        logging.error("Cloudinary is not configured.")
        return False
    
    try:
        full_public_id = f"{folder}/{public_id}"
        result = cloudinary.uploader.destroy(full_public_id)
        
        if result.get('result') == 'ok':
            logging.info(f"Successfully deleted image from Cloudinary: {full_public_id}")
            return True
        else:
            logging.warning(f"Failed to delete image from Cloudinary: {result}")
            return False
            
    except Exception as e:
        logging.error(f"Error deleting image from Cloudinary: {str(e)}")
        return False


def get_cloudinary_url(public_id: str, folder: str = "simfoni-thumbs") -> Optional[str]:
    """
    Get the Cloudinary URL for an uploaded image.
    
    Args:
        public_id: Public ID of the image
        folder: Cloudinary folder the image is in
    
    Returns:
        Cloudinary URL if successful, None otherwise
    """
    if not is_cloudinary_configured():
        return None
    
    try:
        full_public_id = f"{folder}/{public_id}"
        url = cloudinary.utils.cloudinary_url(full_public_id, secure=True)[0]
        return url
    except Exception as e:
        logging.error(f"Error generating Cloudinary URL: {str(e)}")
        return None
