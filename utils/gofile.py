import requests 
import time

class Gofile:
    def __init__(self, token=None, folder_id=None):
        self.token = token
        self.folder_id = folder_id
        
    def find_server(self):
        resp = requests.get('https://api.gofile.io/getServer')
        result = resp.json()
        return result['data']['server']
    
    def upload(self, files):
        server = self.find_server()
        print(f'best server: {server}')
        url = f'https://{server}.gofile.io/uploadFile'
        data_payload = {'token': self.token, 'folderId': self.folder_id}   
        result = []     
        for file in files:
            try:
                print(f'uploading "{file}"...')
                with open(file, 'rb') as f:
                    time_start  = time.time()
                    resp = requests.post(url, files = {'file': f}, data= data_payload)
                    print(f'upload status code: {resp.status_code} - took {time.time() - time_start}')
                    if resp.status_code == 200:
                        download_page = resp.json()['data']['downloadPage']
                        result.append({'file': file, 'download_page': download_page})
                        print('download page: ', download_page)
            except Exception as err:
                print(f'uploading file {file} err: {err}')
        return result