import requests
import logging
import time

def send_with_retries(webhook_url: str, body: dict, max_retries: int = 3) -> requests.Response:
        """
        Attempts to POST `body` to `webhook_url` up to `max_retries` times.
        Returns the final Response object (even if it’s not ok),
        or raises the last exception if all attempts fail catastrophically.
        """
        for attempt in range(1, max_retries + 1):
            try:
                logging.info(f'Attempt {attempt}/{max_retries} — sending webhook to "{webhook_url}"')
                resp = requests.post(webhook_url, json=body)
                logging.info(f'>>> webhook sent: {webhook_url} | attempt {attempt} | code: {resp.status_code}')
                
                if resp.ok:
                    # got a 2xx or 3xx response
                    return resp
                else:
                    logging.warning(f'Non-OK response on attempt {attempt}: {resp.status_code}')
            except Exception as e:
                logging.error(f'Error on attempt {attempt} sending to {webhook_url}: {e}')
            
            # if we’ll retry, back off a bit (e.g. 1s, 2s, 4s…)
            if attempt < max_retries:
                backoff = 2 ** (attempt - 1)
                logging.info(f'Waiting {backoff}s before next retry…')
                time.sleep(backoff)

        # after all attempts, return last response (or you could raise an exception here)
        logging.error(f'All {max_retries} attempts failed for {webhook_url}')
        return resp  # resp might be last non-ok response