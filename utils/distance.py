from numpy.linalg import norm
import numpy as np

def cosine(x, y):
    return np.dot(x,y)/(norm(x)*norm(y))


def calculate_similarities(vector_a, vectors):
  """Calculates similarities between a vector and multiple other vectors.

  Args:
    vector_a: The reference vector.
    vectors: A list of vectors to compare against vector_a.

  Returns:
    A sorted list of tuples, where each tuple is (similarity_score, vector_index),
    sorted from most similar to least similar.
  """
  similarities = []
  for i, vector in enumerate(vectors):
    similarity_score = calculate_similarity(vector_a, vector)
    # print(f'{i} --> {similarity_score}')
    similarities.append((similarity_score, i))

  # Sort by similarity score in descending order (most similar first)
  similarities.sort(key=lambda x: x[0], reverse=True)
  return similarities

def calculate_similarity(vector1, vector2):
  """Calculates the similarity between two vectors.

  This is just an example; replace with your chosen similarity metric.
  Here, we use cosine similarity.

  Args:
    vector1: The first vector.
    vector2: The second vector.

  Returns:
    The cosine similarity score between the two vectors.
  """
  # print(f'a shape: {vector1.shape} | {vector2.shape}')
  return np.dot(vector1, vector2) / (np.linalg.norm(vector1) * np.linalg.norm(vector2))
