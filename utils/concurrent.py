from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, TimeoutError
import time
from multiprocessing import Process, Queue

def run_with_deadline(func, timeout, *args, **kwargs):
    with ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(func, *args, **kwargs)
        try:
            return future.result(timeout=timeout)
        except TimeoutError:
            # Cancel the task if still running
            future.cancel()
            raise TimeoutError(f"Function '{func.__name__}' exceeded {timeout}s deadline")

def run_with_timeout(func, args=(), timeout=None, default=None):
    """
    Runs func(*args) in a separate process.
    - If it finishes within `timeout`, returns its result.
    - Otherwise, kills the process and returns `default` immediately.
    """
    def _wrapper(q, *args):
        try:
            result = func(*args)
            q.put((True, result))
        except Exception as e:
            q.put((False, e))

    q = Queue()
    p = Process(target=_wrapper, args=(q,)+args)
    p.start()

    # Wait up to `timeout` seconds, then come back:
    p.join(timeout)
    if p.is_alive():
        # still running -> kill it
        p.terminate()
        p.join()
        return default

    # otherwise, read back the real result (or exception)
    success, payload = q.get()
    if success:
        return payload
    else:
        raise payload        