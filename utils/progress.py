import requests
import random
import string
import threading

from configs.db import get_db_redis

db_redis = get_db_redis()

def update_progress(id, progress, prefix: str = None):
    print(f'update progress: {id} --> {progress}')
    if id == None:
        return 
        
    # t = threading.Thread(target=ping, args=[id, progress])
    # t.start()    

    print('update progres to redis...')
    # ping(id, progress)
    # id = f'{prefix}{id}'
    # db_redis.set(id, progress)

def ping(id, progress):
    url = f'https://api-similarity-zx53qegsda-as.a.run.app/progress?id={id}&p={progress}'
    try:
        requests.get(url)
    except Exception as err: print(f'ping failed: {url} --> {err}')

def generate_id(size = 15):
    id = ''.join(random.choices(string.ascii_lowercase + string.ascii_uppercase + string.digits, k=size))
    print(f'generated id: {id}')
    return id